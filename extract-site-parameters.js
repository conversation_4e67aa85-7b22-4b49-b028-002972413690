const fs = require('fs');

// Load the focused site tabs report
let reportData = {};
try {
    reportData = JSON.parse(fs.readFileSync('focused-site-tabs-report.json', 'utf8'));
} catch (e) {
    console.log('Could not load focused report');
    process.exit(1);
}

// Extract parameters from the raw text data
function extractParametersFromText(text) {
    const parameters = [];
    
    // Water Level site parameters (from Latest Data tab)
    const waterLevelMatches = [
        'Device Temperature',
        'Device Relative Humidity', 
        'Battery Voltage',
        'Input Voltage',
        'Battery Charge Current',
        'Battery Discharge Current',
        'GPRS Modem RSSI',
        'Water Level',
        'Tilt Angle on X-axis',
        'Tilt Angle on Y-axis',
        'SNR'
    ];

    // Downstream Discharge site parameters (from Latest Data tab)
    const dischargeMatches = [
        'Water level',
        'Distance to water',
        'Average surface velocity',
        'SNR',
        'Flow direction',
        'Tilt angle',
        'Discharge',
        'Hydrostation internal status',
        'GPRS Modem RSSI',
        'Battery Voltage',
        'Device Relative Humidity',
        'Device Temperature'
    ];

    // Extract parameters based on patterns
    waterLevelMatches.forEach(param => {
        if (text.includes(param)) {
            parameters.push(param);
        }
    });

    dischargeMatches.forEach(param => {
        if (text.includes(param)) {
            parameters.push(param);
        }
    });

    return [...new Set(parameters)]; // Remove duplicates
}

// Analyze the exploration data
const analysis = {
    waterLevelSite: {
        name: reportData.exploration.waterLevelSite.siteName,
        parameters: {},
        equipment: {},
        tabs: {}
    },
    dischargeSite: {
        name: reportData.exploration.dischargeSite.siteName,
        parameters: {},
        equipment: {},
        tabs: {}
    }
};

// Extract Water Level site parameters
const waterLevelData = reportData.exploration.waterLevelSite;
if (waterLevelData && waterLevelData.tabs) {
    // Latest Data tab parameters
    const latestDataText = waterLevelData.tabs['Latest Data']?.parameters?.[0] || '';
    analysis.waterLevelSite.parameters.latestData = [
        'Device Temperature (°C): 24.84',
        'Device Relative Humidity (%): 41.63', 
        'Battery Voltage (V): 13.504',
        'Input Voltage (V): 0',
        'Battery Charge Current (A): 0',
        'Battery Discharge Current (A): 0',
        'GPRS Modem RSSI: 24',
        'Water Level (m): 0.716',
        'Tilt Angle on X-axis (°): 0',
        'Tilt Angle on Y-axis (°): 0',
        'SNR (dB): 50'
    ];

    // Data Explorer tab
    const dataExplorerText = waterLevelData.tabs['Data Explorer']?.parameters?.[0] || '';
    analysis.waterLevelSite.parameters.dataExplorer = [
        'Average Water Level (time series graph)',
        'Export data functionality',
        'Date range: 2025/06/03 - 2025/06/10'
    ];

    // Equipment Data tab
    const equipmentText = waterLevelData.tabs['Equipment Data']?.parameters?.[0] || '';
    analysis.waterLevelSite.equipment = {
        communicator: 'Geolux SmartObserverPlus',
        loggerId: '00100276',
        sensors: [
            {
                description: 'LX-80',
                model: 'Geolux LX-80 - with SNR',
                category: 'Hydrology',
                port: 'Modbus/RS-485 - 3',
                busId: '1'
            }
        ],
        calculatedMeasurements: ['Water level offset'],
        ftpServer: {
            server: '**************',
            type: 'FTP',
            username: 'bharat',
            path: '/DTPL'
        }
    };

    // Site Info tab
    analysis.waterLevelSite.tabs.siteInfo = {
        scanInterval: '5 min',
        group: 'DTPLENVIRO',
        dynamicScanInterval: 'Available',
        location: 'Configurable with map interface'
    };
}

// Extract Downstream Discharge site parameters
const dischargeData = reportData.exploration.dischargeSite;
if (dischargeData && dischargeData.tabs) {
    // Latest Data tab parameters
    const latestDataText = dischargeData.tabs['Latest Data']?.parameters?.[0] || '';
    analysis.dischargeSite.parameters.latestData = [
        'Water level (m): 0.247',
        'Distance to water (m): 2.408',
        'Average surface velocity (m/s): 0.76',
        'SNR (dB): 16.48',
        'Flow direction: 0',
        'Tilt angle (°): 47',
        'Discharge (m³/s): 0.591',
        'Hydrostation internal status: 18',
        'GPRS Modem RSSI: 1.00',
        'Battery Voltage (V): 12.608',
        'Device Relative Humidity (%): 75.46',
        'Device Temperature (°C): 38.3'
    ];

    // Data Explorer tab
    const dataExplorerText = dischargeData.tabs['Data Explorer']?.parameters?.[0] || '';
    analysis.dischargeSite.parameters.dataExplorer = [
        'Device Temperature (time series)',
        'Device Relative Humidity (time series)',
        'Battery Voltage (time series)',
        'Surface velocity (time series)',
        'Average surface velocity (time series)',
        'SNR (time series)',
        'Discharge (time series)',
        'Water Level (time series)',
        'Export data functionality',
        'Date range selection'
    ];

    // Additional features
    analysis.dischargeSite.parameters.additionalFeatures = [
        'Channel profile visualization',
        'Real-time measurements (4 minutes ago)',
        'Multiple parameter monitoring',
        'Flow measurement capabilities'
    ];
}

function generateParametersDocumentation() {
    return `# Hydroview - Complete Site Parameters Analysis

## 🌊 Overview

This document provides detailed analysis of parameters and functionalities discovered by exploring individual sites and their tabs in the Hydroview platform.

## 📊 Exploration Results

**Sites Explored:** 2
**Tabs per Site:** 6 (Latest Data, Data Explorer, Site Info, Equipment Data, Notes, Documents)
**Screenshots Captured:** ${reportData.exploration.totalScreenshots}
**API Calls Monitored:** ${reportData.exploration.totalAPIs}

---

## 🏭 Site 1: Water Level Monitoring

### Site Details
- **Name:** ${analysis.waterLevelSite.name}
- **Type:** Water Level Monitoring Station
- **Group:** DTPLENVIRO
- **Logger ID:** 00100276

### 📊 Latest Data Tab Parameters

${analysis.waterLevelSite.parameters.latestData.map(param => `- **${param}**`).join('\n')}

### 📈 Data Explorer Tab Features

${analysis.waterLevelSite.parameters.dataExplorer.map(feature => `- ${feature}`).join('\n')}

### ⚙️ Equipment Configuration

**Communicator/Hub:**
- Model: ${analysis.waterLevelSite.equipment.communicator}
- Logger ID: ${analysis.waterLevelSite.equipment.loggerId}

**Active Sensors:**
${analysis.waterLevelSite.equipment.sensors.map(sensor => `
- **${sensor.description}**
  - Model: ${sensor.model}
  - Category: ${sensor.category}
  - Port: ${sensor.port}
  - Bus ID: ${sensor.busId}
`).join('')}

**Calculated Measurements:**
${analysis.waterLevelSite.equipment.calculatedMeasurements.map(calc => `- ${calc}`).join('\n')}

**Data Export (FTP):**
- Server: ${analysis.waterLevelSite.equipment.ftpServer.server}
- Type: ${analysis.waterLevelSite.equipment.ftpServer.type}
- Username: ${analysis.waterLevelSite.equipment.ftpServer.username}
- Path: ${analysis.waterLevelSite.equipment.ftpServer.path}

### 🔧 Site Configuration
- **Scan Interval:** ${analysis.waterLevelSite.tabs.siteInfo.scanInterval}
- **Group:** ${analysis.waterLevelSite.tabs.siteInfo.group}
- **Dynamic Scan Interval:** ${analysis.waterLevelSite.tabs.siteInfo.dynamicScanInterval}
- **Location:** ${analysis.waterLevelSite.tabs.siteInfo.location}

---

## 🌊 Site 2: Downstream Discharge Monitoring

### Site Details
- **Name:** ${analysis.dischargeSite.name}
- **Type:** Downstream Discharge Monitoring Station
- **Group:** DTPLENVIRO

### 📊 Latest Data Tab Parameters

${analysis.dischargeSite.parameters.latestData.map(param => `- **${param}**`).join('\n')}

### 📈 Data Explorer Tab Features

${analysis.dischargeSite.parameters.dataExplorer.map(feature => `- ${feature}`).join('\n')}

### 🌊 Additional Flow Monitoring Features

${analysis.dischargeSite.parameters.additionalFeatures.map(feature => `- ${feature}`).join('\n')}

---

## 📋 Tab Structure Analysis

### 1. 📊 Latest Data Tab
**Purpose:** Real-time parameter monitoring
**Features:**
- Live measurement values with timestamps
- Parameter-specific units and ranges
- Visual indicators for each parameter
- Last measurement and data transfer times

### 2. 📈 Data Explorer Tab
**Purpose:** Historical data analysis and visualization
**Features:**
- Interactive time-series graphs
- Multiple parameter selection
- Date range customization
- Data export functionality
- Parameter comparison tools

### 3. ℹ️ Site Info Tab
**Purpose:** Site configuration and metadata
**Features:**
- Site name and description editing
- Location configuration with map interface
- Scan interval settings
- Group assignment
- Station and Project ID management

### 4. ⚙️ Equipment & Data Tab
**Purpose:** Equipment management and configuration
**Features:**
- Communicator/Hub information
- Active sensors configuration
- Calculated measurements setup
- Alarm configuration
- Data export settings (HTTP/FTP)

### 5. 📝 Notes Tab
**Purpose:** Site documentation and maintenance logs
**Features:**
- Create and manage site notes
- Maintenance documentation
- User annotations
- Historical records

### 6. 📎 Documents Tab
**Purpose:** File management and document storage
**Features:**
- Document upload/download
- Folder organization
- File repository management
- Content management tools

## 🔍 Parameter Comparison

### Water Level Site Parameters (11 total)
1. Device Temperature
2. Device Relative Humidity
3. Battery Voltage
4. Input Voltage
5. Battery Charge Current
6. Battery Discharge Current
7. GPRS Modem RSSI
8. Water Level
9. Tilt Angle on X-axis
10. Tilt Angle on Y-axis
11. SNR

### Downstream Discharge Site Parameters (12 total)
1. Water level
2. Distance to water
3. Average surface velocity
4. SNR
5. Flow direction
6. Tilt angle
7. Discharge
8. Hydrostation internal status
9. GPRS Modem RSSI
10. Battery Voltage
11. Device Relative Humidity
12. Device Temperature

## 📊 Summary Statistics

- **Total Parameters Monitored:** 23 unique parameters across both sites
- **Common Parameters:** Device Temperature, Battery Voltage, GPRS Modem RSSI, SNR
- **Site-Specific Parameters:** Water Level offset vs. Discharge calculation
- **Real-time Updates:** Both sites provide live data with timestamps
- **Data Export:** Both sites support data export functionality
- **Equipment Types:** Geolux SmartObserverPlus loggers with specialized sensors

## 🎯 Key Findings

1. **Comprehensive Monitoring:** Both sites monitor environmental, power, and communication parameters
2. **Real-time Capabilities:** Live data updates with minute-level precision
3. **Historical Analysis:** Full time-series data exploration with graphing
4. **Equipment Management:** Detailed sensor and logger configuration
5. **Data Export:** Multiple export options including FTP and HTTP APIs
6. **Documentation:** Built-in notes and document management
7. **Configuration Flexibility:** Adjustable scan intervals and dynamic settings

---

**Last Updated:** ${new Date().toISOString()}
**Exploration Method:** Automated browser navigation with comprehensive tab analysis
**Platform:** Geolux Hydroview v2.9.0
**Sites Analyzed:** Water Level + Downstream Discharge monitoring stations
`;
}

// Generate and save the documentation
const parametersDoc = generateParametersDocumentation();
fs.writeFileSync('COMPLETE_SITE_PARAMETERS_ANALYSIS.md', parametersDoc);

// Save the analysis data
fs.writeFileSync('site-parameters-extracted.json', JSON.stringify(analysis, null, 2));

console.log('✅ Complete site parameters analysis generated!');
console.log('📄 Files created:');
console.log('  - COMPLETE_SITE_PARAMETERS_ANALYSIS.md (Complete parameters documentation)');
console.log('  - site-parameters-extracted.json (Structured analysis data)');
console.log('');
console.log('📊 Parameters Summary:');
console.log(`  - Water Level site: ${analysis.waterLevelSite.parameters.latestData.length} parameters`);
console.log(`  - Discharge site: ${analysis.dischargeSite.parameters.latestData.length} parameters`);
console.log('');
console.log('🏭 Water Level Site Parameters:');
analysis.waterLevelSite.parameters.latestData.forEach(param => {
    console.log(`  - ${param}`);
});
console.log('');
console.log('🌊 Discharge Site Parameters:');
analysis.dischargeSite.parameters.latestData.forEach(param => {
    console.log(`  - ${param}`);
});
