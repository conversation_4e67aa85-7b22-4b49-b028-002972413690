# Hydroview - Geolux Radar Water Monitoring System

A comprehensive web-based radar water monitoring and management platform developed by Geolux.

## 🌊 Overview

Hydroview is a sophisticated Single Page Application (SPA) built with the Quasar Framework (Vue.js) that provides real-time monitoring and analysis of water-related data using advanced radar technology. The system offers comprehensive visualization, management, and administrative capabilities for hydrological monitoring networks.

*This documentation was generated through automated exploration of the live system on 10/6/2025.*

## 🏗️ Technology Stack

### Frontend Architecture
- **Framework**: Quasar Framework (Vue.js)
- **UI Components**: Quasar Design System
- **Architecture**: Single Page Application (SPA)
- **Responsive Design**: Mobile and desktop optimized

### Backend & API
- **API Style**: RESTful API with JSON responses
- **Authentication**: RSA encryption + Bearer token system
- **Security**: Permission-based access control
- **Real-time**: Token renewal for session management

### Security Features
- RSA key-based password encryption
- Bearer token authentication
- Permission-based group access
- Secure session management with token renewal

## 🌟 Core Features

### 🔐 Authentication & Security
- **Secure Login**: RSA-encrypted password transmission
- **Session Management**: Automatic token renewal
- **User Profiles**: Individual user accounts with avatars
- **Access Control**: Permission-based system administration

### 🏢 Site Management
- **Monitoring Sites**: Comprehensive site configuration and management
- **Site Administration**: Add, edit, and manage monitoring locations
- **Deleted Sites**: Archive and recovery functionality
- **Site Permissions**: Group-based site access control

### 👥 User & Group Management
- **User Administration**: Complete user lifecycle management
- **Group Management**: Organize users into permission groups
- **Role-Based Access**: Granular permission system
- **User Profiles**: Custom avatars and user information

### 📊 Data Displays & Dashboards
- **Custom Dashboards**: Personalized data visualization layouts
- **Data Display Configuration**: Configurable monitoring displays
- **Real-time Updates**: Live data streaming and updates
- **Interactive Widgets**: Customizable dashboard components

### 🗺️ Geographic Mapping
- **Interactive Maps**: Geographic visualization of monitoring sites
- **Site Locations**: Visual representation of radar installations
- **Map Customization**: Branded mapping with custom styling
- **Geographic Data**: Location-based data analysis

### 📈 Monitoring & Analytics
- **Real-time Monitoring**: Live radar data visualization
- **Historical Data**: Time-series data analysis
- **Data Visualization**: Charts, graphs, and statistical displays
- **Performance Metrics**: System and site performance monitoring

## 🔧 System Architecture

### API Endpoints Structure

#### Authentication
```
GET  /api/v1/login/get_rsa_key     # Get RSA public key for encryption
GET  /api/v1/login/get_token       # Authenticate and get bearer token
GET  /api/v1/login/renew           # Renew authentication token
```

#### Site Management
```
GET  /api/v1/sites/get             # Retrieve monitoring sites
```

#### User Management
```
GET  /api/v1/users/get             # Get user information
GET  /api/v1/users/get_icon        # Retrieve user avatar/icon
```

#### Group Management
```
GET  /api/v1/groups/get                    # Get user groups
GET  /api/v1/groups/get_by_permission      # Get groups by permission level
```

#### Data & Visualization
```
GET  /api/v1/data_displays/get     # Get dashboard configurations
GET  /api/v1/map/get_rebranding    # Get map branding/styling
```

## 🧭 Application Navigation

### Main Sections
- **🏠 Dashboard**: Main overview and system status
- **🏢 Sites**: Monitoring site management and configuration
- **🗺️ Map**: Geographic visualization of monitoring network
- **📊 Data Displays**: Custom dashboard and widget management
- **👥 Groups**: User group and permission management
- **👤 Users**: User administration and profile management
- **🗑️ Deleted Sites**: Archive management and recovery

### User Interface Features
- **Responsive Design**: Optimized for desktop and mobile devices
- **Menu System**: Collapsible navigation with hamburger menu
- **User Profile**: Quick access to user settings and logout
- **Customization**: Dashboard customization capabilities
- **Real-time Updates**: Live data refresh and notifications

## 🚀 Getting Started

### System Requirements
- Modern web browser (Chrome, Firefox, Safari, Edge)
- JavaScript enabled
- Network access to Geolux radar systems
- Valid user credentials

### Accessing the System
1. **Navigate to**: https://hv2.geolux-radars.com/#/
2. **Login**: Enter your username and password
3. **Dashboard**: Access the main monitoring dashboard
4. **Navigation**: Use the menu to explore different sections

### User Workflow
1. **Authentication**: Secure login with encrypted credentials
2. **Site Selection**: Choose monitoring sites to view
3. **Data Visualization**: Access real-time and historical data
4. **Configuration**: Customize dashboards and displays
5. **Administration**: Manage users, groups, and permissions (if authorized)

## 🔒 Security & Permissions

### Authentication Flow
1. Client requests RSA public key
2. Password encrypted with RSA key
3. Encrypted credentials sent to server
4. Bearer token returned for session
5. Token automatically renewed for extended sessions

### Permission System
- **Site Administration**: `#sites-admin` permission
- **Group-based Access**: Users assigned to permission groups
- **Role-based Features**: Different UI elements based on permissions
- **Secure API Access**: All API calls require valid bearer tokens

## 📸 Screenshots

The following screenshots were captured during the automated exploration:

- **Login Page**: `screenshots/01-login-page.png` - Initial login interface
- **Post-Login**: `screenshots/02-after-login.png` - Dashboard after successful authentication
- **Main Dashboard**: `screenshots/dashboard.png` - Primary monitoring interface

## 📞 Support & Administration

### Technical Support
- **System**: Geolux Hydroview Platform
- **Technology**: Quasar Framework (Vue.js)
- **API Version**: v1
- **Authentication**: RSA + Bearer Token

### System Administration
- User and group management through web interface
- Site configuration and monitoring setup
- Dashboard customization and data display configuration
- Permission management and access control

## 🔍 Exploration Details

This documentation was generated through automated browser exploration using Playwright, which:
- Successfully logged into the system using provided credentials
- Captured API calls and network requests
- Identified the Quasar Framework (Vue.js) technology stack
- Discovered navigation structure and user interface elements
- Analyzed authentication flow and security mechanisms
- Documented API endpoints and data structures

---

**Last Updated**: 2025-06-10T08:40:58.556Z
**Generated**: Automated website exploration and API analysis
**Platform**: Geolux Hydroview v2
**Technology**: Quasar Framework (Vue.js) + RESTful API
