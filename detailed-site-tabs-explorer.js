const { chromium } = require('playwright');
const fs = require('fs');

class DetailedSiteTabsExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            siteDetails: {},
            parametersByTab: {},
            dataStructures: {},
            screenshots: [],
            apis: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
        this.screenshotCounter = 1;
        
        // Known sites from previous exploration
        this.sitesToExplore = [
            "DTPL Test Water Level",
            "DTPL TEST Water Level", 
            "DTPLENVIRO 30170 WRD DEMO",
            "DTPLTEST Water Level",
            "DTPLTEST Water level",
            "UJVNL-Gamri Gad Downstream Discharge"
        ];
        
        this.tabsToExplore = [
            "Latest Data",
            "Data Explorer", 
            "Site Info",
            "Equipment Data",
            "Notes",
            "Documents"
        ];
    }

    async init() {
        console.log('🚀 Initializing detailed site tabs exploration...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        this.page = await this.browser.newPage();
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Monitor API calls for parameter data
        this.page.on('request', request => {
            if (request.url().includes('api') || request.url().includes('data') || request.url().includes('parameter')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    timestamp: new Date().toISOString()
                });
            }
        });

        this.page.on('response', async response => {
            if (response.url().includes('api') && response.status() === 200) {
                try {
                    const contentType = response.headers()['content-type'];
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        this.findings.dataStructures[response.url()] = {
                            data: data,
                            timestamp: new Date().toISOString()
                        };
                    }
                } catch (e) {
                    // Ignore parsing errors
                }
            }
        });
    }

    async takeScreenshot(name, description) {
        const filename = `screenshots/detailed-${String(this.screenshotCounter).padStart(2, '0')}-${name}.png`;
        await this.page.screenshot({ path: filename, fullPage: true });
        this.findings.screenshots.push({
            filename: filename,
            description: description,
            timestamp: new Date().toISOString()
        });
        this.screenshotCounter++;
        console.log(`📸 ${filename} - ${description}`);
    }

    async login() {
        console.log('🔐 Logging in...');
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
        await this.page.waitForTimeout(3000);

        await this.page.fill('input[type="text"]', this.credentials.username);
        await this.page.fill('input[type="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        
        await this.page.waitForTimeout(8000);
        await this.takeScreenshot('login-complete', 'Dashboard after login');
        
        console.log('✅ Login successful');
        return true;
    }

    async navigateToSites() {
        console.log('🏢 Navigating to Sites section...');
        
        // Open sidebar
        try {
            const menuButtons = await this.page.$$('button:has-text("menu"), .q-btn:has-text("menu")');
            for (const btn of menuButtons) {
                try {
                    const isVisible = await btn.isVisible();
                    if (isVisible) {
                        await btn.click();
                        await this.page.waitForTimeout(2000);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (error) {
            console.log('Could not open sidebar:', error.message);
        }

        // Navigate to Sites
        try {
            const siteItems = await this.page.$$('.q-item');
            if (siteItems[1]) { // Sites is usually the second item
                await siteItems[1].click();
                await this.page.waitForTimeout(5000);
                await this.takeScreenshot('sites-section', 'Sites section loaded');
                return true;
            }
        } catch (error) {
            console.error('Failed to navigate to Sites:', error.message);
        }
        return false;
    }

    async exploreIndividualSite(siteName, siteIndex) {
        console.log(`\n🏭 === EXPLORING SITE: ${siteName} ===`);
        
        try {
            // Click on the site name in the table
            await this.page.click(`text="${siteName}"`);
            await this.page.waitForTimeout(5000);
            
            const safeSiteName = siteName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            await this.takeScreenshot(`site-${safeSiteName}-overview`, `Site overview: ${siteName}`);

            // Initialize site data structure
            this.findings.siteDetails[siteName] = {
                tabs: {},
                parameters: [],
                dataTypes: [],
                equipment: {},
                lastUpdated: new Date().toISOString()
            };

            // Explore each tab for this site
            for (let tabIndex = 0; tabIndex < this.tabsToExplore.length; tabIndex++) {
                const tabName = this.tabsToExplore[tabIndex];
                await this.exploreTab(siteName, tabName, tabIndex);
                await this.page.waitForTimeout(2000); // Brief pause between tabs
            }

            console.log(`✅ Site exploration complete: ${siteName}`);
            return true;

        } catch (error) {
            console.error(`❌ Error exploring site ${siteName}:`, error.message);
            return false;
        }
    }

    async exploreTab(siteName, tabName, tabIndex) {
        console.log(`  📋 Exploring tab: ${tabName}`);
        
        try {
            // Try to click on the tab
            const tabClickStrategies = [
                async () => await this.page.click(`text="${tabName}"`),
                async () => await this.page.click(`.q-tab:has-text("${tabName}")`),
                async () => await this.page.click(`button:has-text("${tabName}")`),
                async () => {
                    // Try partial text match
                    const partialText = tabName.split(' ')[0];
                    await this.page.click(`text*="${partialText}"`);
                }
            ];

            let tabClicked = false;
            for (let i = 0; i < tabClickStrategies.length && !tabClicked; i++) {
                try {
                    await tabClickStrategies[i]();
                    await this.page.waitForTimeout(4000);
                    tabClicked = true;
                    console.log(`    ✅ Tab clicked using strategy ${i + 1}`);
                } catch (e) {
                    console.log(`    Strategy ${i + 1} failed:`, e.message);
                }
            }

            if (!tabClicked) {
                console.log(`    ❌ Could not click on tab: ${tabName}`);
                return;
            }

            // Take screenshot of the tab
            const safeSiteName = siteName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            const safeTabName = tabName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            await this.takeScreenshot(`${safeSiteName}-${safeTabName}`, `${siteName} - ${tabName} tab`);

            // Analyze tab content based on tab type
            let tabData = {};
            switch (tabName) {
                case "Latest Data":
                    tabData = await this.analyzeLatestDataTab();
                    break;
                case "Data Explorer":
                    tabData = await this.analyzeDataExplorerTab();
                    break;
                case "Site Info":
                    tabData = await this.analyzeSiteInfoTab();
                    break;
                case "Equipment Data":
                    tabData = await this.analyzeEquipmentDataTab();
                    break;
                case "Notes":
                    tabData = await this.analyzeNotesTab();
                    break;
                case "Documents":
                    tabData = await this.analyzeDocumentsTab();
                    break;
                default:
                    tabData = await this.analyzeGenericTab();
            }

            // Store tab data
            this.findings.siteDetails[siteName].tabs[tabName] = tabData;
            
            // Extract parameters for this site
            if (tabData.parameters) {
                this.findings.siteDetails[siteName].parameters = 
                    [...new Set([...this.findings.siteDetails[siteName].parameters, ...tabData.parameters])];
            }

            console.log(`    ✅ Tab analyzed: ${Object.keys(tabData).length} data elements found`);

        } catch (error) {
            console.error(`    ❌ Error exploring tab ${tabName}:`, error.message);
        }
    }

    async analyzeLatestDataTab() {
        console.log('      🔍 Analyzing Latest Data tab...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                parameters: [],
                values: [],
                timestamps: [],
                tables: [],
                realTimeData: []
            };

            // Look for parameter names and values
            document.querySelectorAll('td, .data-cell, .parameter, .value').forEach(cell => {
                const text = cell.textContent?.trim();
                if (text && text.length > 0) {
                    // Check if it's a parameter name (usually contains letters)
                    if (text.match(/[a-zA-Z]/)) {
                        analysis.parameters.push(text);
                    }
                    // Check if it's a numeric value
                    if (text.match(/^\d+\.?\d*$/)) {
                        analysis.values.push(text);
                    }
                    // Check if it's a timestamp
                    if (text.match(/\d{4}-\d{2}-\d{2}|\d{2}:\d{2}:\d{2}/)) {
                        analysis.timestamps.push(text);
                    }
                }
            });

            // Analyze tables specifically
            document.querySelectorAll('table, .q-table').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim());
                const rows = Array.from(table.querySelectorAll('tr')).slice(1).map(row => {
                    return Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim());
                });

                analysis.tables.push({
                    id: `latest-data-table-${index}`,
                    headers: headers,
                    rows: rows,
                    rowCount: rows.length
                });
            });

            // Look for real-time indicators
            document.querySelectorAll('.live, .real-time, .current').forEach(element => {
                const text = element.textContent?.trim();
                if (text) {
                    analysis.realTimeData.push(text);
                }
            });

            return analysis;
        });

        return data;
    }

    async analyzeDataExplorerTab() {
        console.log('      📈 Analyzing Data Explorer tab...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                parameters: [],
                charts: [],
                graphs: [],
                controls: [],
                dateRanges: [],
                parameterOptions: []
            };

            // Look for parameter selection controls
            document.querySelectorAll('select, .q-select, .parameter-select').forEach(select => {
                const options = Array.from(select.querySelectorAll('option')).map(opt => opt.textContent?.trim());
                analysis.parameterOptions.push(...options.filter(opt => opt && opt.length > 0));
            });

            // Look for chart elements
            document.querySelectorAll('canvas, svg, .chart, .graph').forEach((chart, index) => {
                analysis.charts.push({
                    type: chart.tagName,
                    id: chart.id || `chart-${index}`,
                    classes: chart.className,
                    width: chart.width || chart.clientWidth,
                    height: chart.height || chart.clientHeight
                });
            });

            // Look for parameter labels and legends
            document.querySelectorAll('label, .legend, .parameter-label, .axis-label').forEach(label => {
                const text = label.textContent?.trim();
                if (text && text.length < 100) {
                    analysis.parameters.push(text);
                }
            });

            // Look for date/time controls
            document.querySelectorAll('input[type="date"], input[type="datetime-local"], .date-picker').forEach(dateInput => {
                const value = dateInput.value || dateInput.getAttribute('placeholder');
                if (value) {
                    analysis.dateRanges.push(value);
                }
            });

            // Look for control buttons
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent?.trim();
                if (text && text.length < 50) {
                    analysis.controls.push(text);
                }
            });

            return analysis;
        });

        return data;
    }

    async analyzeSiteInfoTab() {
        console.log('      ℹ️ Analyzing Site Info tab...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                siteDetails: {},
                location: {},
                configuration: {},
                metadata: []
            };

            // Look for key-value pairs (site information)
            document.querySelectorAll('tr, .info-row, .detail-row').forEach(row => {
                const cells = row.querySelectorAll('td, .label, .value');
                if (cells.length >= 2) {
                    const key = cells[0].textContent?.trim();
                    const value = cells[1].textContent?.trim();
                    if (key && value) {
                        analysis.siteDetails[key] = value;
                    }
                }
            });

            // Look for location information
            document.querySelectorAll('.location, .coordinates, .address').forEach(element => {
                const text = element.textContent?.trim();
                if (text) {
                    analysis.location.description = text;
                }
            });

            // Look for configuration parameters
            document.querySelectorAll('.config, .setting, .parameter').forEach(element => {
                const text = element.textContent?.trim();
                if (text) {
                    analysis.configuration[element.className] = text;
                }
            });

            // Collect all text content for metadata
            const allText = document.body.textContent?.trim();
            if (allText) {
                analysis.metadata = allText.split('\n').filter(line => line.trim().length > 0).slice(0, 20);
            }

            return analysis;
        });

        return data;
    }

    async analyzeEquipmentDataTab() {
        console.log('      ⚙️ Analyzing Equipment Data tab...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                equipment: [],
                sensors: [],
                status: {},
                health: {},
                configuration: {}
            };

            // Look for equipment information
            document.querySelectorAll('.equipment, .device, .sensor').forEach(element => {
                const text = element.textContent?.trim();
                if (text) {
                    analysis.equipment.push(text);
                }
            });

            // Look for status indicators
            document.querySelectorAll('.status, .health, .online, .offline').forEach(element => {
                const text = element.textContent?.trim();
                if (text) {
                    analysis.status[element.className] = text;
                }
            });

            // Look for sensor information in tables
            document.querySelectorAll('table tr').forEach(row => {
                const cells = Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim());
                if (cells.length > 0 && cells[0]) {
                    analysis.sensors.push(cells);
                }
            });

            return analysis;
        });

        return data;
    }

    async analyzeNotesTab() {
        console.log('      📝 Analyzing Notes tab...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                notes: [],
                timestamps: [],
                authors: [],
                categories: []
            };

            // Look for note content
            document.querySelectorAll('.note, .comment, .entry').forEach(note => {
                const text = note.textContent?.trim();
                if (text) {
                    analysis.notes.push(text);
                }
            });

            // Look for timestamps
            document.querySelectorAll('.timestamp, .date, .time').forEach(time => {
                const text = time.textContent?.trim();
                if (text) {
                    analysis.timestamps.push(text);
                }
            });

            return analysis;
        });

        return data;
    }

    async analyzeDocumentsTab() {
        console.log('      📎 Analyzing Documents tab...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                documents: [],
                fileTypes: [],
                uploadOptions: [],
                downloadLinks: []
            };

            // Look for document names
            document.querySelectorAll('.document, .file, .attachment').forEach(doc => {
                const text = doc.textContent?.trim();
                if (text) {
                    analysis.documents.push(text);
                }
            });

            // Look for file type indicators
            document.querySelectorAll('.pdf, .doc, .xlsx, .txt').forEach(file => {
                const text = file.textContent?.trim();
                if (text) {
                    analysis.fileTypes.push(text);
                }
            });

            // Look for upload/download buttons
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent?.trim();
                if (text && (text.includes('Upload') || text.includes('Download'))) {
                    analysis.uploadOptions.push(text);
                }
            });

            return analysis;
        });

        return data;
    }

    async analyzeGenericTab() {
        console.log('      🔍 Analyzing generic tab content...');
        
        const data = await this.page.evaluate(() => {
            const analysis = {
                headings: [],
                tables: [],
                forms: [],
                buttons: [],
                content: []
            };

            // Get headings
            document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(h => {
                analysis.headings.push(h.textContent?.trim());
            });

            // Get tables
            document.querySelectorAll('table').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim());
                analysis.tables.push({
                    id: `table-${index}`,
                    headers: headers,
                    rowCount: table.querySelectorAll('tr').length
                });
            });

            // Get buttons
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent?.trim();
                if (text) {
                    analysis.buttons.push(text);
                }
            });

            return analysis;
        });

        return data;
    }

    async generateDetailedReport() {
        console.log('📊 Generating detailed site tabs report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            exploration: {
                totalSites: Object.keys(this.findings.siteDetails).length,
                totalTabs: this.tabsToExplore.length,
                totalScreenshots: this.findings.screenshots.length,
                totalAPIs: this.findings.apis.length
            },
            siteDetails: this.findings.siteDetails,
            parametersByTab: this.findings.parametersByTab,
            dataStructures: this.findings.dataStructures,
            screenshots: this.findings.screenshots,
            apiCalls: this.findings.apis,
            summary: {
                sitesExplored: Object.keys(this.findings.siteDetails),
                tabsExplored: this.tabsToExplore,
                parametersFound: this.extractAllParameters(),
                screenshotsCaptured: this.findings.screenshots.map(s => s.filename)
            }
        };

        fs.writeFileSync('detailed-site-tabs-report.json', JSON.stringify(report, null, 2));
        console.log('✅ Detailed site tabs report saved');
        return report;
    }

    extractAllParameters() {
        const allParameters = [];
        Object.values(this.findings.siteDetails).forEach(site => {
            if (site.parameters) {
                allParameters.push(...site.parameters);
            }
        });
        return [...new Set(allParameters)]; // Remove duplicates
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new DetailedSiteTabsExplorer();
    
    try {
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        await explorer.init();
        await explorer.login();
        
        // Navigate to Sites section
        const sitesNavigated = await explorer.navigateToSites();
        if (!sitesNavigated) {
            console.log('❌ Could not navigate to Sites section');
            return;
        }
        
        // Explore each site and all its tabs
        for (let i = 0; i < Math.min(explorer.sitesToExplore.length, 3); i++) { // Limit to first 3 sites for detailed exploration
            const siteName = explorer.sitesToExplore[i];
            console.log(`\n--- SITE ${i + 1}/${Math.min(explorer.sitesToExplore.length, 3)} ---`);
            await explorer.exploreIndividualSite(siteName, i);
            await explorer.page.waitForTimeout(3000); // Pause between sites
        }
        
        const report = await explorer.generateDetailedReport();
        
        console.log('\n🎉 Detailed site tabs exploration completed!');
        console.log('📊 Summary:');
        console.log(`  - Sites explored: ${report.exploration.totalSites}`);
        console.log(`  - Tabs per site: ${report.exploration.totalTabs}`);
        console.log(`  - Screenshots: ${report.exploration.totalScreenshots}`);
        console.log(`  - Parameters found: ${report.summary.parametersFound.length}`);
        console.log(`  - API calls: ${report.exploration.totalAPIs}`);
        
    } catch (error) {
        console.error('❌ Detailed exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = DetailedSiteTabsExplorer;
