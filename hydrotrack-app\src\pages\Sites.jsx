import React, { useState } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Card,
  CardBody,
  Badge,
  Button,
  Icon
} from "../components/Component";

const Sites = () => {
  const [sites] = useState([
    {
      id: 1,
      name: "River Ganges - Haridwar",
      type: "Water Level",
      location: "Haridwar, Uttarakhand",
      status: "Active",
      lastData: "2 min ago",
      health: "Good",
      coordinates: "29.9457°N, 78.1642°E"
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON> Discharge - Delhi",
      type: "Discharge",
      location: "Delhi",
      status: "Active",
      lastData: "5 min ago",
      health: "Warning",
      coordinates: "28.7041°N, 77.1025°E"
    },
    {
      id: 3,
      name: "Weather Station - Mumbai",
      type: "Weather",
      location: "Mumbai, Maharashtra",
      status: "Active",
      lastData: "1 min ago",
      health: "Good",
      coordinates: "19.0760°N, 72.8777°E"
    },
    {
      id: 4,
      name: "Krishna River - Vijayawada",
      type: "Water Level",
      location: "Vijayawada, Andhra Pradesh",
      status: "Inactive",
      lastData: "2 hours ago",
      health: "Poor",
      coordinates: "16.5062°N, 80.6480°E"
    },
    {
      id: 5,
      name: "Narmada Discharge - Bharuch",
      type: "Discharge",
      location: "Bharuch, Gujarat",
      status: "Active",
      lastData: "3 min ago",
      health: "Good",
      coordinates: "21.7051°N, 72.9959°E"
    }
  ]);

  const getStatusBadge = (status) => {
    switch (status.toLowerCase()) {
      case "active":
        return <Badge color="success">Active</Badge>;
      case "inactive":
        return <Badge color="danger">Inactive</Badge>;
      case "maintenance":
        return <Badge color="warning">Maintenance</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  const getHealthBadge = (health) => {
    switch (health.toLowerCase()) {
      case "good":
        return <Badge color="success">Good</Badge>;
      case "warning":
        return <Badge color="warning">Warning</Badge>;
      case "poor":
        return <Badge color="danger">Poor</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  return (
    <React.Fragment>
      <Head title="Sites - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Site Management</BlockTitle>
              <p className="text-soft">
                Manage and monitor all water monitoring sites
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button color="primary">
                  <Icon name="plus"></Icon>
                  <span>Add Site</span>
                </Button>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        <Block>
          <Row className="g-gs">
            {sites.map((site) => (
              <Col key={site.id} xxl="4" lg="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-3">
                      <div className="card-title">
                        <h6 className="title">{site.name}</h6>
                        <p className="text-soft">{site.coordinates}</p>
                      </div>
                      <div className="card-tools">
                        {getStatusBadge(site.status)}
                      </div>
                    </div>
                    
                    <div className="row g-3 mb-3">
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Type</label>
                          <div className="form-control-wrap">
                            <Badge color="outline-info">{site.type}</Badge>
                          </div>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Health</label>
                          <div className="form-control-wrap">
                            {getHealthBadge(site.health)}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="row g-3 mb-3">
                      <div className="col-12">
                        <div className="form-group">
                          <label className="form-label text-soft">Location</label>
                          <div className="form-control-wrap">
                            <span>{site.location}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="row g-3 mb-3">
                      <div className="col-12">
                        <div className="form-group">
                          <label className="form-label text-soft">Last Data Received</label>
                          <div className="form-control-wrap">
                            <span className="text-primary">{site.lastData}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="card-tools mt-3">
                      <ul className="btn-toolbar gx-1">
                        <li>
                          <Button size="sm" color="primary" outline>
                            <Icon name="eye"></Icon>
                            <span>View Details</span>
                          </Button>
                        </li>
                        <li>
                          <Button size="sm" color="gray" outline>
                            <Icon name="edit"></Icon>
                            <span>Edit</span>
                          </Button>
                        </li>
                      </ul>
                    </div>
                  </CardBody>
                </Card>
              </Col>
            ))}
          </Row>
        </Block>

        {/* Summary Statistics */}
        <Block>
          <Row className="g-gs">
            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Total Sites</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">{sites.length}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-primary">
                          <Icon name="map-pin-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Active Sites</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {sites.filter(site => site.status === "Active").length}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-success">
                          <Icon name="activity-round-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Water Level Sites</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {sites.filter(site => site.type === "Water Level").length}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-info">
                          <Icon name="waves"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Weather Stations</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {sites.filter(site => site.type === "Weather").length}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-warning">
                          <Icon name="sun-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>
      </Content>
    </React.Fragment>
  );
};

export default Sites;
