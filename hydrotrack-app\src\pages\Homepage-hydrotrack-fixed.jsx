import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Button,
  Icon
} from "../components/Component";
import { Card, CardBody, Badge } from "reactstrap";

const Homepage = () => {
  const [dashboardData, setDashboardData] = useState({
    totalSites: 0,
    activeSites: 0,
    alerts: 0,
    dataPoints: 0,
    loading: true
  });

  useEffect(() => {
    // Fetch dashboard data
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Simulate API call
      setTimeout(() => {
        setDashboardData({
          totalSites: 24,
          activeSites: 22,
          alerts: 3,
          dataPoints: 15420,
          loading: false
        });
      }, 1000);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setDashboardData(prev => ({ ...prev, loading: false }));
    }
  };

  return (
    <React.Fragment>
      <Head title="HydroTrack - Water Monitoring Dashboard"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Water Monitoring Dashboard</BlockTitle>
              <p className="text-soft">
                Real-time overview of your water monitoring network
              </p>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        <Block>
          <Row className="g-gs">
            {/* Quick Statistics */}
            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Total Sites</h6>
                    </div>
                    <div className="card-tools">
                      <Badge color="success" className="badge-dim">
                        Active
                      </Badge>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {dashboardData.loading ? "..." : dashboardData.totalSites}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-primary">
                          <Icon name="map-pin-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Active Sites</h6>
                    </div>
                    <div className="card-tools">
                      <Badge color="success" className="badge-dim">
                        Online
                      </Badge>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {dashboardData.loading ? "..." : dashboardData.activeSites}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-success">
                          <Icon name="activity-round-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Active Alerts</h6>
                    </div>
                    <div className="card-tools">
                      <Badge color="warning" className="badge-dim">
                        Warning
                      </Badge>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {dashboardData.loading ? "..." : dashboardData.alerts}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-warning">
                          <Icon name="bell-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Data Points Today</h6>
                    </div>
                    <div className="card-tools">
                      <Badge color="info" className="badge-dim">
                        Live
                      </Badge>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">
                        {dashboardData.loading ? "..." : dashboardData.dataPoints.toLocaleString()}
                      </span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-info">
                          <Icon name="growth-fill"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* System Overview */}
            <Col xxl="8" lg="7">
              <Card className="card-bordered h-100">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">System Overview</h6>
                    </div>
                  </div>
                  <div className="nk-tb-list">
                    <div className="nk-tb-item nk-tb-head">
                      <div className="nk-tb-col"><span>Site</span></div>
                      <div className="nk-tb-col"><span>Status</span></div>
                      <div className="nk-tb-col"><span>Last Data</span></div>
                      <div className="nk-tb-col"><span>Health</span></div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <span className="tb-lead">River Ganges - Haridwar</span>
                      </div>
                      <div className="nk-tb-col">
                        <Badge color="success">Active</Badge>
                      </div>
                      <div className="nk-tb-col">
                        <span className="tb-sub">2 min ago</span>
                      </div>
                      <div className="nk-tb-col">
                        <Badge color="success">Good</Badge>
                      </div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <span className="tb-lead">Yamuna Discharge - Delhi</span>
                      </div>
                      <div className="nk-tb-col">
                        <Badge color="success">Active</Badge>
                      </div>
                      <div className="nk-tb-col">
                        <span className="tb-sub">5 min ago</span>
                      </div>
                      <div className="nk-tb-col">
                        <Badge color="warning">Warning</Badge>
                      </div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <span className="tb-lead">Weather Station - Mumbai</span>
                      </div>
                      <div className="nk-tb-col">
                        <Badge color="success">Active</Badge>
                      </div>
                      <div className="nk-tb-col">
                        <span className="tb-sub">1 min ago</span>
                      </div>
                      <div className="nk-tb-col">
                        <Badge color="success">Good</Badge>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Real-time Alerts */}
            <Col xxl="4" lg="5">
              <Card className="card-bordered h-100">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Recent Alerts</h6>
                    </div>
                  </div>
                  <div className="nk-tb-list">
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-warning">
                            <Icon name="bell"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">High Water Level</span>
                            <span className="tb-sub">Yamuna - Delhi</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-info">
                            <Icon name="info"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Low Battery</span>
                            <span className="tb-sub">Station GNG-HDW-001</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-success">
                            <Icon name="check"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">System Restored</span>
                            <span className="tb-sub">Weather Station Mumbai</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>
      </Content>
    </React.Fragment>
  );
};

export default Homepage;
