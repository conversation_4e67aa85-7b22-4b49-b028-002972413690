// HydroTrack Water Monitoring Platform - Menu Configuration
// Based on Hydroview SRS Requirements - Updated Navigation

const menu = [
  {
    heading: "HydroTrack Dashboard",
  },
  {
    icon: "dashlite",
    text: "Overview Dashboard",
    link: "/",
  },
  {
    icon: "activity-round-fill",
    text: "Real-time Monitoring",
    link: "/monitoring",
  },
  {
    heading: "Site Management",
  },
  {
    icon: "map-pin-fill",
    text: "Sites",
    subMenu: [
      {
        text: "All Sites",
        link: "/sites",
      },
      {
        text: "Water Level Sites",
        link: "/sites/water-level",
      },
      {
        text: "Discharge Sites", 
        link: "/sites/discharge",
      },
      {
        text: "Weather Stations",
        link: "/sites/weather",
      },
      {
        text: "Add New Site",
        link: "/sites/create",
      },
    ],
  },
  {
    icon: "globe",
    text: "GIS Mapping",
    link: "/gis-map",
  },
  {
    heading: "Data & Analytics",
  },
  {
    icon: "growth-fill",
    text: "Data Explorer",
    link: "/data-explorer",
  },
  {
    icon: "pie-fill",
    text: "Analytics Dashboard",
    link: "/analytics",
  },
  {
    icon: "file-docs",
    text: "Reports",
    subMenu: [
      {
        text: "Generate Reports",
        link: "/reports/generate",
      },
      {
        text: "Scheduled Reports",
        link: "/reports/scheduled",
      },
      {
        text: "Report Templates",
        link: "/reports/templates",
      },
    ],
  },
  {
    heading: "Alerts & Notifications",
  },
  {
    icon: "bell-fill",
    text: "Alerts",
    subMenu: [
      {
        text: "Active Alerts",
        link: "/alerts/active",
      },
      {
        text: "Alert Rules",
        link: "/alerts/rules",
      },
      {
        text: "Alert History",
        link: "/alerts/history",
      },
    ],
  },
  {
    icon: "chat-fill",
    text: "Notifications",
    link: "/notifications",
  },
  {
    heading: "Equipment Management",
  },
  {
    icon: "server-fill",
    text: "Equipment",
    subMenu: [
      {
        text: "All Equipment",
        link: "/equipment",
      },
      {
        text: "Loggers",
        link: "/equipment/loggers",
      },
      {
        text: "Sensors",
        link: "/equipment/sensors",
      },
      {
        text: "Equipment Health",
        link: "/equipment/health",
      },
    ],
  },
  {
    heading: "Administration",
  },
  {
    icon: "users-fill",
    text: "User Management",
    subMenu: [
      {
        text: "All Users",
        link: "/admin/users",
      },
      {
        text: "User Groups",
        link: "/admin/groups",
      },
      {
        text: "Roles & Permissions",
        link: "/admin/roles",
      },
    ],
  },
  {
    icon: "opt-alt-fill",
    text: "System Settings",
    subMenu: [
      {
        text: "General Settings",
        link: "/admin/settings/general",
      },
      {
        text: "Data Management",
        link: "/admin/settings/data",
      },
      {
        text: "Security Settings",
        link: "/admin/settings/security",
      },
      {
        text: "System Health",
        link: "/admin/system/health",
      },
    ],
  },
  {
    icon: "file-text",
    text: "Audit Logs",
    link: "/admin/audit-logs",
  },
  {
    heading: "API & Integration",
  },
  {
    icon: "code",
    text: "API Management",
    subMenu: [
      {
        text: "API Keys",
        link: "/api/keys",
      },
      {
        text: "API Documentation",
        link: "/api/docs",
      },
      {
        text: "Third-party Integrations",
        link: "/api/integrations",
      },
    ],
  },
  {
    heading: "Original DashLite Demos",
  },
  {
    icon: "bitcoin-cash",
    text: "Crypto Dashboard",
    link: "/crypto",
  },
  {
    icon: "coins",
    text: "Invest Dashboard",
    link: "/invest",
  },
  {
    heading: "Pre-built Pages",
  },
  {
    icon: "tile-thumb",
    text: "Projects",
    subMenu: [
      {
        text: "Project Cards",
        link: "/project-card",
      },
      {
        text: "Project List",
        link: "/project-list",
      },
    ],
  },
  {
    icon: "users",
    text: "User Manage",
    subMenu: [
      {
        text: "User List - Regular",
        link: "/user-list-regular",
      },
      {
        text: "User List - Compact",
        link: "/user-list-compact",
      },
      {
        text: "User Details - Regular",
        link: "/user-details-regular",
      },
      {
        text: "User Profile - Regular",
        link: "/user-profile-regular",
      },
      {
        text: "User Contact - Card",
        link: "/user-contact-card",
      },
    ],
  },
  {
    icon: "file-docs",
    text: "KYC Application",
    subMenu: [
      {
        text: "KYC List - Regular",
        link: "/kyc-list-regular",
      },
      {
        text: "KYC Details",
        link: "/kyc-details-regular",
      },
    ],
  },
  {
    icon: "tranx",
    text: "Transactions",
    subMenu: [
      {
        text: "Tranx List - Basic",
        link: "/transaction-basic",
      },
      {
        text: "Tranx List - Crypto",
        link: "/transaction-crypto",
      },
    ],
  },
  {
    icon: "grid-alt-fill",
    text: "Applications",
    subMenu: [
      {
        text: "Messages",
        link: "/app-messages",
      },
      {
        text: "Chats",
        link: "/app-chat",
      },
      {
        text: "Calendar",
        link: "/app-calender",
      },
      {
        text: "Kanban Board",
        link: "/app-kanban",
      },
      {
        text: "Inbox",
        link: "/app-inbox",
      },
      {
        text: "File Manager",
        link: "/app-file-manager",
      },
    ],
  },
  {
    icon: "card-view",
    text: "Products",
    subMenu: [
      {
        text: "Product List",
        link: "/product-list",
      },
      {
        text: "Product Card",
        link: "/product-card",
      },
      {
        text: "Product Details",
        link: "/product-details",
      },
    ],
  },
  {
    icon: "bag",
    text: "Invoice",
    subMenu: [
      {
        text: "Invoice List",
        link: "/invoice-list",
      },
      {
        text: "Invoice Details",
        link: "/invoice-details",
      },
    ],
  },
  {
    icon: "view-col",
    text: "Pricing Table",
    link: "/pricing-table",
  },
  {
    icon: "img",
    text: "Image Gallery",
    link: "/image-gallery",
  },
  {
    heading: "Misc Pages",
  },
  {
    icon: "signin",
    text: "Auth Pages",
    subMenu: [
      {
        text: "Login / Signin",
        link: "/auth-login",
        newTab: true,
      },
      {
        text: "Register / Signup",
        link: "/auth-register",
        newTab: true,
      },
      {
        text: "Forgot Password",
        link: "/auth-reset",
        newTab: true,
      },
      {
        text: "Success / Confirm",
        link: "/auth-success",
        newTab: true,
      },
    ],
  },
  {
    icon: "files",
    text: "Error Pages",
    subMenu: [
      {
        text: "404 Classic",
        link: "/errors/404-classic",
        newTab: true,
      },
      {
        text: "504 Classic",
        link: "/errors/504-classic",
        newTab: true,
      },
      {
        text: "404 Modern",
        link: "/errors/404-modern",
        newTab: true,
      },
      {
        text: "504 Modern",
        link: "/errors/504-modern",
        newTab: true,
      },
    ],
  },
  {
    icon: "files",
    text: "Other Pages",
    subMenu: [
      {
        text: "Blank / Startup",
        link: "/pages/blank",
      },
      {
        text: "Faqs / Help",
        link: "/pages/faq",
      },
      {
        text: "Terms / Policy",
        link: "/pages/terms-policy",
      },
      {
        text: "Regular Page - v1",
        link: "/pages/regular-v1",
      },
      {
        text: "Regular Page - v2",
        link: "/pages/regular-v2",
      },
    ],
  },
  {
    heading: "Components",
  },
  {
    icon: "layers",
    text: "Ui Elements",
    subMenu: [
      {
        text: "Alerts",
        link: "/components/alerts",
      },
      {
        text: "Accordions",
        link: "/components/accordions",
      },
      {
        text: "Avatar",
        link: "/components/avatar",
      },
      {
        text: "Badges",
        link: "/components/badges",
      },
      {
        text: "Buttons",
        link: "/components/buttons",
      },
      {
        text: "Button Group",
        link: "/components/button-group",
      },
      {
        text: "Breadcrumbs",
        link: "/components/breadcrumbs",
      },
      {
        text: "Cards",
        link: "/components/cards",
      },
      {
        text: "Carousel",
        link: "/components/carousel",
      },
      {
        text: "Dropdowns",
        link: "/components/dropdowns",
      },
      {
        text: "Modals",
        link: "/components/modals",
      },
      {
        text: "Pagination",
        link: "/components/pagination",
      },
      {
        text: "Popover",
        link: "/components/popover",
      },
      {
        text: "Progress",
        link: "/components/progress",
      },
      {
        text: "Spinner",
        link: "/components/spinner",
      },
      {
        text: "Tabs",
        link: "/components/tabs",
      },
      {
        text: "Toast",
        link: "/components/toast",
      },
      {
        text: "Typography",
        link: "/components/typography",
      },
      {
        text: "Tooltips",
        link: "/components/tooltips",
      },
      {
        text: "Utilities",
        subMenu: [
          {
            text: "Borders",
            link: "/components/util-border",
          },
          {
            text: "Colors",
            link: "/components/util-colors",
          },
          {
            text: "Display",
            link: "/components/util-display",
          },
          {
            text: "Embeded",
            link: "/components/util-embeded",
          },
          {
            text: "Flex",
            link: "/components/util-flex",
          },
          {
            text: "Text",
            link: "/components/util-text",
          },
          {
            text: "Sizing",
            link: "/components/util-sizing",
          },
          {
            text: "Spacing",
            link: "/components/util-spacing",
          },
          {
            text: "Others",
            link: "/components/util-others",
          },
        ],
      },
    ],
  },
  {
    icon: "dot-box",
    text: "Crafted Icons",
    subMenu: [
      {
        text: "SVG Icon-Exclusive",
        link: "/components/misc/svg-icons",
      },
      {
        text: "Nioicon - HandCrafted",
        link: "/components/misc/nioicon",
      },
    ],
  },
  {
    icon: "table-view",
    text: "Tables",
    subMenu: [
      {
        text: "Basic Tables",
        link: "/components/table/table-basic",
      },
      {
        text: "Special Tables",
        link: "/components/table/table-special",
      },
      {
        text: "DataTables",
        link: "/components/table/table-datatable",
      },
    ],
  },
  {
    icon: "card-view",
    text: "Forms",
    subMenu: [
      {
        text: "Form Elements",
        link: "/components/form/form-elements",
      },
      {
        text: "Checkbox Radio",
        link: "/components/form/checkbox-radio",
      },
      {
        text: "Advanced Controls",
        link: "/components/form/advanced-controls",
      },
      {
        text: "Input Group",
        link: "/components/form/input-group",
      },
      {
        text: "Form Upload",
        link: "/components/form/form-upload",
      },
      {
        text: "Date Time Picker",
        link: "/components/form/datetime-picker",
      },
      {
        text: "Number Spinner",
        link: "/components/form/number-spinner",
      },
      {
        text: "noUiSlider",
        link: "/components/form/nouislider",
      },
      {
        text: "Form Layouts",
        link: "/components/form/form-layouts",
      },
      {
        text: "Form Validation",
        link: "/components/form/form-validation",
      },
      {
        text: "Wizard Basic",
        link: "/components/form/wizard/wizard-basic",
      },
    ],
  },
  {
    icon: "pie",
    text: "Charts",
    subMenu: [
      {
        text: "Chart Js",
        link: "/components/charts/chartjs",
      },
      {
        text: "Knobs",
        link: "/components/charts/knobs",
      },
    ],
  },
  {
    icon: "puzzle",
    text: "Widgets",
    subMenu: [
      {
        text: "Card Widgets",
        link: "/components/widgets/cards",
      },
      {
        text: "Chart Widgets",
        link: "/components/widgets/charts",
      },
      {
        text: "Rating Widgets",
        link: "/components/widgets/rating",
      },
    ],
  },
  {
    icon: "block-over",
    text: "Miscellaneous",
    subMenu: [
      {
        text: "Slick Sliders",
        link: "/components/misc/slick-slider",
      },
      {
        text: "JsTree",
        link: "/components/misc/jsTree",
      },
      {
        text: "React Toastify",
        link: "/components/misc/toastify",
      },
      {
        text: "Sweet Alert",
        link: "/components/misc/sweet-alert",
      },
      {
        text: "React DualListBox",
        link: "/components/misc/dual-list",
      },
      {
        text: "Google Map",
        link: "/components/misc/map",
      },
    ],
  },
];

// Admin-only menu items (conditionally shown based on user role)
const adminMenu = [
  {
    heading: "Super Admin",
  },
  {
    icon: "shield-check",
    text: "System Administration",
    subMenu: [
      {
        text: "Database Management",
        link: "/superadmin/database",
      },
      {
        text: "System Configuration",
        link: "/superadmin/config",
      },
      {
        text: "Backup & Recovery",
        link: "/superadmin/backup",
      },
      {
        text: "Performance Monitoring",
        link: "/superadmin/performance",
      },
    ],
  },
];

export { menu, adminMenu };
export default menu;
