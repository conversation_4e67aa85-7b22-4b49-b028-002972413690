// HydroTrack Water Monitoring Platform - Menu Configuration
// Based on Hydroview SRS Requirements

const menu = [
  {
    heading: "Main Dashboard",
  },
  {
    icon: "dashlite",
    text: "Overview Dashboard",
    link: "/",
  },
  {
    icon: "activity-round-fill",
    text: "Real-time Monitoring",
    link: "/monitoring",
  },
  {
    heading: "Site Management",
  },
  {
    icon: "map-pin-fill",
    text: "Sites",
    subMenu: [
      {
        text: "All Sites",
        link: "/sites",
      },
      {
        text: "Water Level Sites",
        link: "/sites/water-level",
      },
      {
        text: "Discharge Sites", 
        link: "/sites/discharge",
      },
      {
        text: "Weather Stations",
        link: "/sites/weather",
      },
      {
        text: "Add New Site",
        link: "/sites/create",
      },
    ],
  },
  {
    icon: "globe",
    text: "GIS Mapping",
    link: "/gis-map",
  },
  {
    heading: "Data & Analytics",
  },
  {
    icon: "growth-fill",
    text: "Data Explorer",
    link: "/data-explorer",
  },
  {
    icon: "pie-fill",
    text: "Analytics Dashboard",
    link: "/analytics",
  },
  {
    icon: "file-docs",
    text: "Reports",
    subMenu: [
      {
        text: "Generate Reports",
        link: "/reports/generate",
      },
      {
        text: "Scheduled Reports",
        link: "/reports/scheduled",
      },
      {
        text: "Report Templates",
        link: "/reports/templates",
      },
    ],
  },
  {
    heading: "Alerts & Notifications",
  },
  {
    icon: "bell-fill",
    text: "Alerts",
    subMenu: [
      {
        text: "Active Alerts",
        link: "/alerts/active",
      },
      {
        text: "Alert Rules",
        link: "/alerts/rules",
      },
      {
        text: "Alert History",
        link: "/alerts/history",
      },
    ],
  },
  {
    icon: "chat-fill",
    text: "Notifications",
    link: "/notifications",
  },
  {
    heading: "Equipment Management",
  },
  {
    icon: "server-fill",
    text: "Equipment",
    subMenu: [
      {
        text: "All Equipment",
        link: "/equipment",
      },
      {
        text: "Loggers",
        link: "/equipment/loggers",
      },
      {
        text: "Sensors",
        link: "/equipment/sensors",
      },
      {
        text: "Equipment Health",
        link: "/equipment/health",
      },
    ],
  },
  {
    heading: "Administration",
  },
  {
    icon: "users-fill",
    text: "User Management",
    subMenu: [
      {
        text: "All Users",
        link: "/admin/users",
      },
      {
        text: "User Groups",
        link: "/admin/groups",
      },
      {
        text: "Roles & Permissions",
        link: "/admin/roles",
      },
    ],
  },
  {
    icon: "opt-alt-fill",
    text: "System Settings",
    subMenu: [
      {
        text: "General Settings",
        link: "/admin/settings/general",
      },
      {
        text: "Data Management",
        link: "/admin/settings/data",
      },
      {
        text: "Security Settings",
        link: "/admin/settings/security",
      },
      {
        text: "System Health",
        link: "/admin/system/health",
      },
    ],
  },
  {
    icon: "file-text",
    text: "Audit Logs",
    link: "/admin/audit-logs",
  },
  {
    heading: "API & Integration",
  },
  {
    icon: "code",
    text: "API Management",
    subMenu: [
      {
        text: "API Keys",
        link: "/api/keys",
      },
      {
        text: "API Documentation",
        link: "/api/docs",
      },
      {
        text: "Third-party Integrations",
        link: "/api/integrations",
      },
    ],
  },
];

// Admin-only menu items (conditionally shown based on user role)
const adminMenu = [
  {
    heading: "Super Admin",
  },
  {
    icon: "shield-check",
    text: "System Administration",
    subMenu: [
      {
        text: "Database Management",
        link: "/superadmin/database",
      },
      {
        text: "System Configuration",
        link: "/superadmin/config",
      },
      {
        text: "Backup & Recovery",
        link: "/superadmin/backup",
      },
      {
        text: "Performance Monitoring",
        link: "/superadmin/performance",
      },
    ],
  },
];

export { menu, adminMenu };
export default menu;
