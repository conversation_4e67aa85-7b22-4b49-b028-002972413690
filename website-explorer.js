const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

class WebsiteExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            pages: [],
            features: [],
            navigation: [],
            forms: [],
            charts: [],
            maps: [],
            tables: [],
            buttons: [],
            apis: [],
            technologies: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
    }

    async init() {
        console.log('🚀 Initializing browser...');
        this.browser = await chromium.launch({ 
            headless: false, // Set to true for headless mode
            slowMo: 1000 // Slow down for better observation
        });
        this.page = await this.browser.newPage();
        
        // Set viewport
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Listen to network requests to capture API calls
        this.page.on('request', request => {
            if (request.url().includes('api') || request.url().includes('data')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    headers: request.headers()
                });
            }
        });

        // Listen to console logs
        this.page.on('console', msg => {
            console.log('Browser console:', msg.text());
        });
    }

    async login() {
        console.log('🔐 Attempting to login...');
        try {
            await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
            await this.page.waitForTimeout(3000);

            // Take screenshot of login page
            await this.page.screenshot({ path: 'screenshots/01-login-page.png', fullPage: true });

            // Look for login form elements
            const loginSelectors = [
                'input[type="text"]',
                'input[type="email"]', 
                'input[name="username"]',
                'input[name="user"]',
                'input[id*="user"]',
                'input[placeholder*="user"]',
                'input[placeholder*="User"]'
            ];

            const passwordSelectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[id*="password"]',
                'input[placeholder*="password"]'
            ];

            let usernameField = null;
            let passwordField = null;

            // Find username field
            for (const selector of loginSelectors) {
                try {
                    usernameField = await this.page.$(selector);
                    if (usernameField) {
                        console.log(`Found username field with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            // Find password field
            for (const selector of passwordSelectors) {
                try {
                    passwordField = await this.page.$(selector);
                    if (passwordField) {
                        console.log(`Found password field with selector: ${selector}`);
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }

            if (usernameField && passwordField) {
                // Fill credentials
                await usernameField.fill(this.credentials.username);
                await passwordField.fill(this.credentials.password);

                // Look for login button
                const loginButtons = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    'button:has-text("Login")',
                    'button:has-text("Sign In")',
                    'button:has-text("Log In")',
                    '.login-btn',
                    '#login-btn'
                ];

                for (const selector of loginButtons) {
                    try {
                        const loginBtn = await this.page.$(selector);
                        if (loginBtn) {
                            console.log(`Clicking login button: ${selector}`);
                            await loginBtn.click();
                            break;
                        }
                    } catch (e) {
                        continue;
                    }
                }

                // Wait for navigation after login
                await this.page.waitForTimeout(5000);
                await this.page.screenshot({ path: 'screenshots/02-after-login.png', fullPage: true });
                
                console.log('✅ Login successful!');
                return true;
            } else {
                console.log('❌ Could not find login form elements');
                return false;
            }
        } catch (error) {
            console.error('❌ Login failed:', error.message);
            return false;
        }
    }

    async exploreNavigation() {
        console.log('🧭 Exploring navigation...');
        try {
            // Find navigation elements
            const navSelectors = [
                'nav',
                '.navbar',
                '.navigation',
                '.menu',
                '.sidebar',
                '[role="navigation"]',
                'ul.nav',
                '.nav-menu'
            ];

            for (const selector of navSelectors) {
                try {
                    const navElement = await this.page.$(selector);
                    if (navElement) {
                        const navItems = await navElement.$$('a, button, li');
                        for (const item of navItems) {
                            const text = await item.textContent();
                            const href = await item.getAttribute('href');
                            if (text && text.trim()) {
                                this.findings.navigation.push({
                                    text: text.trim(),
                                    href: href,
                                    selector: selector
                                });
                            }
                        }
                    }
                } catch (e) {
                    continue;
                }
            }

            console.log(`Found ${this.findings.navigation.length} navigation items`);
        } catch (error) {
            console.error('Error exploring navigation:', error.message);
        }
    }

    async exploreCurrentPage() {
        console.log('📄 Exploring current page...');
        try {
            const url = this.page.url();
            const title = await this.page.title();
            
            // Get page content
            const headings = await this.page.$$eval('h1, h2, h3, h4, h5, h6', elements => 
                elements.map(el => ({ tag: el.tagName, text: el.textContent.trim() }))
            );

            // Find forms
            const forms = await this.page.$$eval('form', elements => 
                elements.map((form, index) => ({
                    id: form.id || `form-${index}`,
                    action: form.action,
                    method: form.method,
                    inputs: Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
                        type: input.type,
                        name: input.name,
                        placeholder: input.placeholder,
                        required: input.required
                    }))
                }))
            );

            // Find buttons
            const buttons = await this.page.$$eval('button, input[type="button"], input[type="submit"]', elements =>
                elements.map(btn => ({
                    text: btn.textContent || btn.value,
                    type: btn.type,
                    className: btn.className
                }))
            );

            // Find tables
            const tables = await this.page.$$eval('table', elements =>
                elements.map((table, index) => ({
                    id: table.id || `table-${index}`,
                    headers: Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim()),
                    rowCount: table.querySelectorAll('tr').length
                }))
            );

            // Check for charts/graphs
            const chartSelectors = [
                'canvas',
                '.chart',
                '.graph',
                '[id*="chart"]',
                '[class*="chart"]',
                'svg'
            ];

            const charts = [];
            for (const selector of chartSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    for (const element of elements) {
                        const id = await element.getAttribute('id');
                        const className = await element.getAttribute('class');
                        charts.push({ selector, id, className });
                    }
                } catch (e) {
                    continue;
                }
            }

            // Check for maps
            const mapSelectors = [
                '.map',
                '#map',
                '[id*="map"]',
                '[class*="map"]',
                '.leaflet-container',
                '.ol-viewport'
            ];

            const maps = [];
            for (const selector of mapSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        maps.push({ selector });
                    }
                } catch (e) {
                    continue;
                }
            }

            this.findings.pages.push({
                url,
                title,
                headings,
                forms,
                buttons,
                tables,
                charts,
                maps
            });

            console.log(`✅ Explored page: ${title}`);
        } catch (error) {
            console.error('Error exploring page:', error.message);
        }
    }

    async navigateAndExplore() {
        console.log('🗺️ Navigating through all pages...');
        
        // First explore the current page
        await this.exploreCurrentPage();
        await this.exploreNavigation();

        // Navigate through menu items
        for (let i = 0; i < this.findings.navigation.length && i < 10; i++) {
            const navItem = this.findings.navigation[i];
            try {
                console.log(`Navigating to: ${navItem.text}`);
                
                if (navItem.href && navItem.href.startsWith('#')) {
                    // Handle hash navigation
                    await this.page.goto(this.baseUrl + navItem.href.substring(1));
                } else if (navItem.href && navItem.href.startsWith('http')) {
                    await this.page.goto(navItem.href);
                } else {
                    // Try clicking the element
                    await this.page.click(`text="${navItem.text}"`);
                }

                await this.page.waitForTimeout(3000);
                await this.page.screenshot({ 
                    path: `screenshots/page-${i + 3}-${navItem.text.replace(/[^a-zA-Z0-9]/g, '-')}.png`, 
                    fullPage: true 
                });
                
                await this.exploreCurrentPage();
            } catch (error) {
                console.error(`Error navigating to ${navItem.text}:`, error.message);
            }
        }
    }

    async generateReport() {
        console.log('📊 Generating exploration report...');
        
        // Create screenshots directory if it doesn't exist
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            summary: {
                totalPages: this.findings.pages.length,
                totalNavItems: this.findings.navigation.length,
                totalForms: this.findings.pages.reduce((sum, page) => sum + page.forms.length, 0),
                totalTables: this.findings.pages.reduce((sum, page) => sum + page.tables.length, 0),
                totalCharts: this.findings.pages.reduce((sum, page) => sum + page.charts.length, 0),
                totalMaps: this.findings.pages.reduce((sum, page) => sum + page.maps.length, 0)
            },
            findings: this.findings
        };

        // Save detailed report
        fs.writeFileSync('website-exploration-report.json', JSON.stringify(report, null, 2));
        
        console.log('✅ Report saved to website-exploration-report.json');
        return report;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new WebsiteExplorer();
    
    try {
        await explorer.init();
        
        const loginSuccess = await explorer.login();
        if (loginSuccess) {
            await explorer.navigateAndExplore();
            const report = await explorer.generateReport();
            console.log('🎉 Exploration completed successfully!');
            console.log('📊 Summary:', report.summary);
        } else {
            console.log('❌ Could not login to the website');
        }
    } catch (error) {
        console.error('❌ Exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = WebsiteExplorer;
