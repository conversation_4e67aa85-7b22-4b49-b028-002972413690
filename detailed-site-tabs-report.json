{"timestamp": "2025-06-10T09:33:53.195Z", "website": "https://hv2.geolux-radars.com/#/", "exploration": {"totalSites": 0, "totalTabs": 6, "totalScreenshots": 2, "totalAPIs": 14}, "siteDetails": {}, "parametersByTab": {}, "dataStructures": {"https://hv2.geolux-radars.com/api/v1/map/get_rebranding": {"data": {"status": "ok", "brand": "", "version": "2.9.0"}, "timestamp": "2025-06-10T09:31:42.773Z"}, "https://hv2.geolux-radars.com/api/v1/login/get_rsa_key": {"data": {"rsa_key": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDgsd3IqfiNkopjMK6/1j1603An\nTbPMNmKU3qEG8CjXPMUeFA/HJaphupJELsLGakUdvmWyKuSOlCfPzrSDpU7AU2AR\nHiCJibCXVLqyk6LXh31jj248E+Gl+QStbfXrx3mEdddcS0fyFW1qpNlu7GTRUwQO\nU4bXWdGFJXuafKOcoQIDAQAB\n-----<PERSON><PERSON> PUBLIC KEY-----\n"}, "timestamp": "2025-06-10T09:31:41.223Z"}, "https://hv2.geolux-radars.com/api/v1/login/get_token?username=deepak.gupta&password=8188ff1a8c48d184a89f55accfea784c83ea31d91766d4e9a5140b93115177d2142ae84ef6c4a69df69b8168c27d9828e3c4a74214f5adf6f0666ca31f356c27e6fc4452e49b2a82b1d0ff8d838675db1f6fef72b6ff1022bf0c01101ac1536faed5c911e7124a003de6aa184ef663e9ecb60ccb34dfa5304a8ca8587afa4fbd": {"data": {"status": "ok", "token": "PqTqIXJWBjWXk25sj4T4ErusIWBX/sZBzumDMnfCMqW1JsDvBhQEDgBN4OSiOdOh+OmhJI4clUUqnPE4xrCmGzrfXdy0whnBu1wWto4m2ZUpTXbPH0MPQHaoAKbScG3bCAF5CkNxli/pSV0+RvnoqU34oKlfMwOliyRmryvRbS1Q/9/nvhc9VsIzYyssvni3LFNElsyaQLMiOycV+TnwA6kl5UEzIjxWz7LJ62rUrqo=", "user_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "hide_alarms": false}, "timestamp": "2025-06-10T09:31:41.449Z"}, "https://hv2.geolux-radars.com/api/v1/data_displays/get?data_display_id=HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa": {"data": {"status": "ok", "data_displays_get": "{\"title\":\"User Dashboard\",\"calendar\":\"cal\",\"interval\":{\"duration\":7,\"start\":null,\"end\":null},\"tabs\":[{\"name\":\"Overview\",\"rows\":[]}]}"}, "timestamp": "2025-06-10T09:31:42.538Z"}, "https://hv2.geolux-radars.com/api/v1/sites/get": {"data": {"status": "ok", "sites_get": {"groups_sites": [{"group": {"name": "DTPLENVIRO", "description": "", "parent_group": "root", "deleted": false, "data_displays": [{"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "FVXbgdxm7BgJLUJfwvjayKPw9U8DQ95rV5ML3ZwPUgCj"}, {"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "Fy47KDWtXdKopBJ27k9PCeDfMDqo2QhQzf6JhyVYyf6Q"}], "preferred_units": {}, "color": "", "http_export": []}, "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "sites": [{"site_id": "27foLCiCjpQa4nsa6uNidATNapmhScLP2eQiTExpEbhd", "logger_id": "00100278", "last_timestamp": 140494331112816, "last_timestamp_info": {"server_timestamp": 0, "server_secondary_timestamp": 7599310901211001000, "server_secondary_protocol": "", "measurement_timestamp": 0, "last_lorawan_check": 94222492242684, "blobs_last_timestamp": {}, "scan_interval_min": 140494331112952, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {}, "communicator_health": {}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPLTEST", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water Level", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [{"name": "Latest Data", "display_id": "FtbLc2kESpZwGCfKxs4KtMGKEqfTETexzSd81JxYP6YG"}, {"name": "Data history", "display_id": "D8c1vx7RE9Sxmp3FyS7giqr3fpg5bVxeFekMeHUUh6vH"}], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "2jpbApHQKqbG28BsjE66euy2hCQcAa4Pe63FVsLLspUu", "logger_id": "00100245", "last_timestamp": 140494331112816, "last_timestamp_info": {"server_timestamp": 0, "server_secondary_timestamp": 7599310901211001000, "server_secondary_protocol": "", "measurement_timestamp": 0, "last_lorawan_check": 94222492242684, "blobs_last_timestamp": {}, "scan_interval_min": 140494331112952, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {}, "communicator_health": {}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPL Test", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water Level", "latitude": 28.409597, "longitude": 77.369331, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [{"name": "Latest Data", "display_id": "9E6fPKfEiT74rtD6PNkUArr5SBifmK9RDWhrDG7DbSpN"}, {"name": "Data history", "display_id": "uQN9dJ7kNVJy66U3HFXmKi3YcBCKt5ngww5ab8ez8Zq"}], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "31zX2zPMqRWg29dRu8CJy7e84JJuEfD2xEEFSSyRhR9m", "logger_id": "00100286", "last_timestamp": 140494331112816, "last_timestamp_info": {"server_timestamp": 0, "server_secondary_timestamp": 7599310901211001000, "server_secondary_protocol": "", "measurement_timestamp": 0, "last_lorawan_check": 94222492242684, "blobs_last_timestamp": {}, "scan_interval_min": 140494331112952, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {}, "communicator_health": {}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "UJVNL-<PERSON><PERSON><PERSON>", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Downstream Discharge", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 15, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [{"name": "Latest Data", "display_id": "3JTKrHDiWdyPpWB6zfQFtguraiURYs3NhQgwMk67wmWZ"}, {"name": "Data history", "display_id": "6KgTs2skxTDnjiuGwZZiJUiCAUXEv1UwfRDLHTNyMjGK"}], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "3zT2MTjHVDcpeWGV9Cm7SfKJJp84nB1b2DDv37Z39q43", "logger_id": "30170", "last_timestamp": 140494331112816, "last_timestamp_info": {"server_timestamp": 0, "server_secondary_timestamp": 7599310901211001000, "server_secondary_protocol": "", "measurement_timestamp": 0, "last_lorawan_check": 94222492242684, "blobs_last_timestamp": {}, "scan_interval_min": 140494331112952, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {}, "communicator_health": {}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPLENVIRO 30170", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "WRD DEMO", "latitude": 8.146944, "longitude": 77.478889, "scan_interval_min": 30, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [{"name": "Latest Data", "display_id": "2ouZRhc91dC6F8o2ShzExps1PDbUfnkTmEYEdmpU4jPD"}, {"name": "Data history", "display_id": "CFCEhop4fXnpQXTAjhJ6TTh3uzA3omvh2MxjwjskJy31"}], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "Cw6Bh12J1EWvcwsCs3z1tfTjPpcuDGr6C2ixyHC1pzAB", "logger_id": "00100276", "last_timestamp": 140494331112816, "last_timestamp_info": {"server_timestamp": 0, "server_secondary_timestamp": 7599310901211001000, "server_secondary_protocol": "", "measurement_timestamp": 0, "last_lorawan_check": 94222492242684, "blobs_last_timestamp": {}, "scan_interval_min": 140494331112952, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {}, "communicator_health": {}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPL TEST", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water Level", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [{"name": "Latest Data", "display_id": "Z36ZzK9TAW5GGj19nrhkm3CNBSpdFuXvZo1qVjkQape"}, {"name": "Data history", "display_id": "9Yr4r6acnUnHGqwyBJB64DYor12WoxCL1NhumsAan9Mx"}], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "GG9f6iExzyqREE4m18fgqd3trr4hkzLVgEYiRFGmzn3V", "logger_id": "00100277", "last_timestamp": 140494331112816, "last_timestamp_info": {"server_timestamp": 0, "server_secondary_timestamp": 7599310901211001000, "server_secondary_protocol": "", "measurement_timestamp": 0, "last_lorawan_check": 94222492242684, "blobs_last_timestamp": {}, "scan_interval_min": 140494331112952, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {}, "communicator_health": {}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPLTEST", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water level", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [{"name": "Latest Data", "display_id": "HgE5y9JJCcv9LdLB911YQtQ3Gfy8ue1oFmrQxtSqA1VU"}, {"name": "Data history", "display_id": "DSf9cFXyFrswtnst48MMkEHD9LpEPLtdstrwo5aHNzXG"}], "station_id": "", "project_id": ""}, "active_alarms": []}]}, {"group": {"name": "Demo", "description": "DTPLTEST", "parent_group": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "deleted": false, "data_displays": [], "preferred_units": {}, "color": "#5376a7", "http_export": []}, "group_id": "FAQ82UbbrJQSXzHd1mSRssECrVdzBqBySA1JPFyW93kY", "sites": []}]}}, "timestamp": "2025-06-10T09:31:42.538Z"}, "https://hv2.geolux-radars.com/api/v1/groups/get": {"data": {"status": "ok", "groups": {"6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe": {"name": "DTPLENVIRO", "description": "", "parent_group": "root", "deleted": false, "data_displays": [{"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "FVXbgdxm7BgJLUJfwvjayKPw9U8DQ95rV5ML3ZwPUgCj"}, {"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "Fy47KDWtXdKopBJ27k9PCeDfMDqo2QhQzf6JhyVYyf6Q"}], "preferred_units": {}, "color": "", "http_export": []}, "FAQ82UbbrJQSXzHd1mSRssECrVdzBqBySA1JPFyW93kY": {"name": "Demo", "description": "DTPLTEST", "parent_group": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "deleted": false, "data_displays": [], "preferred_units": {}, "color": "#5376a7", "http_export": []}}}, "timestamp": "2025-06-10T09:31:42.538Z"}, "https://hv2.geolux-radars.com/api/v1/login/renew": {"data": {"status": "ok", "token": "PqTqIXJWBjWXk25sj4T4ErusIWBX/sZBzumDMnfCMqW1JsDvBhQEDgBN4OSiOdOh+OmhJI4clUUqnPE4xrCmGzrfXdy0whnBu1wWto4m2ZUpTXbPH0MPQHaoAKbScG3bCAF5CkNxli/pSV0+RvnoqU34oKlfMwOliyRmryvRbS1D6zz6zVk8zauXSWCMF0HVCa14xxVDbt7mcQu0wQe4iungBswCvRoKL9zze7fr2qg="}, "timestamp": "2025-06-10T09:31:42.764Z"}, "https://hv2.geolux-radars.com/api/v1/groups/get_by_permission?show_deleted=true&permission=%23sites-admin": {"data": {"status": "ok", "groups": {"6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe": {"name": "DTPLENVIRO", "description": "", "parent_group": "root", "deleted": false, "data_displays": [{"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "FVXbgdxm7BgJLUJfwvjayKPw9U8DQ95rV5ML3ZwPUgCj"}, {"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "Fy47KDWtXdKopBJ27k9PCeDfMDqo2QhQzf6JhyVYyf6Q"}], "preferred_units": {}, "color": "", "http_export": []}, "FAQ82UbbrJQSXzHd1mSRssECrVdzBqBySA1JPFyW93kY": {"name": "Demo", "description": "DTPLTEST", "parent_group": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "deleted": false, "data_displays": [], "preferred_units": {}, "color": "#5376a7", "http_export": []}}}, "timestamp": "2025-06-10T09:31:42.984Z"}, "https://hv2.geolux-radars.com/api/v1/users/get?user_id=HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa": {"data": {"status": "ok", "users_get": {"groups_users": [{"group": {"name": "VEPAR test", "description": "", "parent_group": "root", "deleted": false, "data_displays": [], "preferred_units": {}, "color": "", "http_export": []}, "group_id": "2FMjxnWYAa5VtHN5TuWBENfYaxcZtxRQU7Zg56XSVG22", "users": [{"user_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "user": {"login": "deepak.gupta", "name": "<PERSON><PERSON>", "password": "****", "group_id": "2FMjxnWYAa5VtHN5TuWBENfYaxcZtxRQU7Zg56XSVG22", "email": "", "phone": "", "title": "", "language": "en-US", "preferred_units": {}, "favourite_sites": [], "org_unit": "", "display_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "group_permissions": {"6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe": {"permissions": ["#data-publish", "#data-view", "#groups-admin", "#sites-admin", "#users-admin"]}}, "two_factor": {"required": false, "type": "", "secret": "****"}, "use_site_timezone": true, "alert_destinations": [], "subscribed_alerts": []}}]}]}}, "timestamp": "2025-06-10T09:31:43.021Z"}, "https://hv2.geolux-radars.com/api/v1/sites/get_verbose?health=true": {"data": {"status": "ok", "sites_get": {"groups_sites": [{"group": {"name": "DTPLENVIRO", "description": "", "parent_group": "root", "deleted": false, "data_displays": [{"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "FVXbgdxm7BgJLUJfwvjayKPw9U8DQ95rV5ML3ZwPUgCj"}, {"name": "Data Display", "author_id": "HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "display_id": "Fy47KDWtXdKopBJ27k9PCeDfMDqo2QhQzf6JhyVYyf6Q"}], "preferred_units": {}, "color": "", "http_export": []}, "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "sites": [{"site_id": "27foLCiCjpQa4nsa6uNidATNapmhScLP2eQiTExpEbhd", "logger_id": "00100278", "last_timestamp": **********, "last_timestamp_info": {"server_timestamp": **********, "server_secondary_timestamp": 0, "server_secondary_protocol": "", "measurement_timestamp": **********, "last_lorawan_check": 139912231957145, "blobs_last_timestamp": {}, "scan_interval_min": 5, "version": "110", "imei": "", "logger_location": ""}, "measurements_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "communicator_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPLTEST", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water Level", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "2jpbApHQKqbG28BsjE66euy2hCQcAa4Pe63FVsLLspUu", "logger_id": "00100245", "last_timestamp": **********, "last_timestamp_info": {"server_timestamp": **********, "server_secondary_timestamp": 0, "server_secondary_protocol": "", "measurement_timestamp": **********, "last_lorawan_check": 0, "blobs_last_timestamp": {}, "scan_interval_min": 5, "version": "111", "imei": "", "logger_location": ""}, "measurements_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "communicator_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPL Test", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water Level", "latitude": 28.409597, "longitude": 77.369331, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "31zX2zPMqRWg29dRu8CJy7e84JJuEfD2xEEFSSyRhR9m", "logger_id": "00100286", "last_timestamp": **********, "last_timestamp_info": {"server_timestamp": **********, "server_secondary_timestamp": 0, "server_secondary_protocol": "", "measurement_timestamp": **********, "last_lorawan_check": 0, "blobs_last_timestamp": {}, "scan_interval_min": 15, "version": "110", "imei": "", "logger_location": ""}, "measurements_health": {"**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000"}, "communicator_health": {"**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000"}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "UJVNL-<PERSON><PERSON><PERSON>", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Downstream Discharge", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 15, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "3zT2MTjHVDcpeWGV9Cm7SfKJJp84nB1b2DDv37Z39q43", "logger_id": "30170", "last_timestamp": **********, "last_timestamp_info": {"server_timestamp": **********, "server_secondary_timestamp": 0, "server_secondary_protocol": "", "measurement_timestamp": **********, "last_lorawan_check": 0, "blobs_last_timestamp": {}, "scan_interval_min": 30, "version": "416", "imei": "867280064821903", "logger_location": ""}, "measurements_health": {"**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000"}, "communicator_health": {"**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000", "**********": "1.000000"}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPLENVIRO 30170", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "WRD DEMO", "latitude": 8.146944, "longitude": 77.478889, "scan_interval_min": 30, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "Cw6Bh12J1EWvcwsCs3z1tfTjPpcuDGr6C2ixyHC1pzAB", "logger_id": "00100276", "last_timestamp": **********, "last_timestamp_info": {"server_timestamp": **********, "server_secondary_timestamp": 0, "server_secondary_protocol": "", "measurement_timestamp": **********, "last_lorawan_check": 140103824345120, "blobs_last_timestamp": {}, "scan_interval_min": 5, "version": "110", "imei": "", "logger_location": ""}, "measurements_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "communicator_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPL TEST", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water Level", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [], "station_id": "", "project_id": ""}, "active_alarms": []}, {"site_id": "GG9f6iExzyqREE4m18fgqd3trr4hkzLVgEYiRFGmzn3V", "logger_id": "00100277", "last_timestamp": 0, "last_timestamp_info": {"server_timestamp": **********, "server_secondary_timestamp": 0, "server_secondary_protocol": "", "measurement_timestamp": **********, "last_lorawan_check": 140103824345120, "blobs_last_timestamp": {}, "scan_interval_min": 5, "version": "", "imei": "", "logger_location": ""}, "measurements_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "communicator_health": {"**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000", "**********": "0.000000"}, "secondary_communicator_health": {}, "important_site_measurement": [], "site": {"name": "DTPLTEST", "group_id": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "description": "Water level", "latitude": 45.785179, "longitude": 15.95611, "scan_interval_min": 5, "rule_set": [], "use_dynamic_scan_interval": false, "timezone": "undefined", "channel_geometry": {"cross_section": []}, "data_displays": [], "station_id": "", "project_id": ""}, "active_alarms": []}]}, {"group": {"name": "Demo", "description": "DTPLTEST", "parent_group": "6ykTZMpsLFfYXJDpqD5zYoLthRkPiKS8qpefD6QFbbSe", "deleted": false, "data_displays": [], "preferred_units": {}, "color": "#5376a7", "http_export": []}, "group_id": "FAQ82UbbrJQSXzHd1mSRssECrVdzBqBySA1JPFyW93kY", "sites": []}]}}, "timestamp": "2025-06-10T09:32:06.140Z"}, "https://hv2.geolux-radars.com/api/v1/aggregate": {"data": [{"code": 200, "headers": {"Content-Type": "text/csv"}, "result": "dGltZXN0YW1wLDJqcGJBcEhRS3FiRzI4QnNqRTY2ZXV5MmhDUWNBYTRQZTYzRlZzTExzcFV1LUVGZjgzZ1NIdFpreTJYcVd3WHNIR2ZBVDdKVFNhVXBKWHoxa2EzenVmZXY1Cg=="}, {"code": 200, "headers": {"Content-Type": "text/csv"}, "result": "dGltZXN0YW1wLEN3NkJoMTJKMUVXdmN3c0NzM3oxdGZUalBwY3VER3I2QzJpeHlIQzFwekFCLUpDOWNpbktac0w3Y2FKTDRqa1l0cnBKVWVzbWU5c2dGVEc5ZXRUMjlid3E4Cg=="}, {"code": 200, "headers": {"Content-Type": "text/csv"}, "result": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}, {"code": 200, "headers": {"Content-Type": "text/csv"}, "result": "dGltZXN0YW1wLDI3Zm9MQ2lDanBRYTRuc2E2dU5pZEFUTmFwbWhTY0xQMmVRaVRFeHBFYmhkLUN0bzJ6dTk5TTJEQjQzeTMzNDhjc25HUXhjelp2aGJKblFpWlBHa3lZMzU3Cg=="}, {"code": 200, "headers": {"Content-Type": "text/csv"}, "result": "dGltZXN0YW1wLEdHOWY2aUV4enlxUkVFNG0xOGZncWQzdHJyNGhrekxWZ0VZaVJGR216bjNWLTl6dU13M25jY3hramd3UXFDTjlHblF1dnNid2FDdUF0WHdNckZwZHdCdW1TCg=="}, {"code": 200, "headers": {"Content-Type": "text/csv"}, "result": "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}], "timestamp": "2025-06-10T09:32:06.669Z"}}, "screenshots": [{"filename": "screenshots/detailed-01-login-complete.png", "description": "Dashboard after login", "timestamp": "2025-06-10T09:31:49.487Z"}, {"filename": "screenshots/detailed-02-sites-section.png", "description": "Sites section loaded", "timestamp": "2025-06-10T09:32:11.134Z"}], "apiCalls": [{"url": "https://hv2.geolux-radars.com/api/v1/map/get_rebranding", "method": "GET", "timestamp": "2025-06-10T09:31:32.657Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/login/get_rsa_key", "method": "GET", "timestamp": "2025-06-10T09:31:40.341Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/login/get_token?username=deepak.gupta&password=8188ff1a8c48d184a89f55accfea784c83ea31d91766d4e9a5140b93115177d2142ae84ef6c4a69df69b8168c27d9828e3c4a74214f5adf6f0666ca31f356c27e6fc4452e49b2a82b1d0ff8d838675db1f6fef72b6ff1022bf0c01101ac1536faed5c911e7124a003de6aa184ef663e9ecb60ccb34dfa5304a8ca8587afa4fbd", "method": "GET", "timestamp": "2025-06-10T09:31:41.222Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/sites/get", "method": "GET", "timestamp": "2025-06-10T09:31:42.276Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/data_displays/get?data_display_id=HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "method": "GET", "timestamp": "2025-06-10T09:31:42.276Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/groups/get", "method": "GET", "timestamp": "2025-06-10T09:31:42.276Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/login/renew", "method": "GET", "timestamp": "2025-06-10T09:31:42.276Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/map/get_rebranding", "method": "GET", "timestamp": "2025-06-10T09:31:42.276Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/users/get_icon?user_id=HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa&ts=**********", "method": "GET", "timestamp": "2025-06-10T09:31:42.276Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/groups/get_by_permission?show_deleted=true&permission=%23sites-admin", "method": "GET", "timestamp": "2025-06-10T09:31:42.537Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/users/get?user_id=HcCejf2QYfKxZoFCCQecWaEDMegXm58g3q8tAgLpAcUa", "method": "GET", "timestamp": "2025-06-10T09:31:42.762Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/sites/get_verbose?health=true", "method": "GET", "timestamp": "2025-06-10T09:32:05.900Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/aggregate", "method": "POST", "timestamp": "2025-06-10T09:32:06.195Z"}, {"url": "https://hv2.geolux-radars.com/api/v1/aggregate", "method": "POST", "timestamp": "2025-06-10T09:32:06.437Z"}], "summary": {"sitesExplored": [], "tabsExplored": ["Latest Data", "Data Explorer", "Site Info", "Equipment Data", "Notes", "Documents"], "parametersFound": [], "screenshotsCaptured": ["screenshots/detailed-01-login-complete.png", "screenshots/detailed-02-sites-section.png"]}}