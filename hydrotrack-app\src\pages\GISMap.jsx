import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Button,
  Icon
} from "../components/Component";
import { Card, CardBody, Badge, Form, FormGroup, Label, Input } from "reactstrap";

const GISMap = () => {
  const [selectedSite, setSelectedSite] = useState(null);
  const [mapLayer, setMapLayer] = useState("satellite");
  const [showAlerts, setShowAlerts] = useState(true);
  const [showEquipment, setShowEquipment] = useState(true);

  const sites = [
    {
      id: 1,
      name: "Ganges - Haridwar",
      type: "Water Level",
      coordinates: [29.9457, 78.1642],
      status: "active",
      health: "good",
      lastData: "2 min ago",
      waterLevel: 12.5,
      alerts: 0
    },
    {
      id: 2,
      name: "Yamuna - Delhi",
      type: "Discharge",
      coordinates: [28.7041, 77.1025],
      status: "active",
      health: "warning",
      lastData: "5 min ago",
      discharge: 185,
      alerts: 1
    },
    {
      id: 3,
      name: "Weather Station - Mumbai",
      type: "Weather",
      coordinates: [19.0760, 72.8777],
      status: "active",
      health: "good",
      lastData: "1 min ago",
      temperature: 28.5,
      alerts: 0
    },
    {
      id: 4,
      name: "Krishna - Vijayawada",
      type: "Water Level",
      coordinates: [16.5062, 80.6480],
      status: "inactive",
      health: "poor",
      lastData: "2 hours ago",
      waterLevel: 0,
      alerts: 2
    },
    {
      id: 5,
      name: "Narmada - Bharuch",
      type: "Discharge",
      coordinates: [21.7051, 72.9959],
      status: "active",
      health: "good",
      lastData: "3 min ago",
      discharge: 220,
      alerts: 0
    }
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case "active": return "#28a745";
      case "warning": return "#ffc107";
      case "inactive": return "#dc3545";
      default: return "#6c757d";
    }
  };

  const getHealthBadge = (health) => {
    switch (health) {
      case "good":
        return <Badge color="success">Good</Badge>;
      case "warning":
        return <Badge color="warning">Warning</Badge>;
      case "poor":
        return <Badge color="danger">Poor</Badge>;
      default:
        return <Badge color="secondary">Unknown</Badge>;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <Badge color="success">Active</Badge>;
      case "inactive":
        return <Badge color="danger">Inactive</Badge>;
      default:
        return <Badge color="secondary">Unknown</Badge>;
    }
  };

  // Simulate map component (since we don't have actual Leaflet integration yet)
  const MapPlaceholder = () => (
    <div 
      style={{ 
        height: '500px', 
        backgroundColor: '#e9ecef',
        border: '2px dashed #6c757d',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column',
        borderRadius: '8px'
      }}
    >
      <Icon name="map" style={{ fontSize: '48px', color: '#6c757d', marginBottom: '16px' }}></Icon>
      <h5 style={{ color: '#6c757d', marginBottom: '8px' }}>Interactive GIS Map</h5>
      <p style={{ color: '#6c757d', textAlign: 'center', margin: 0 }}>
        Leaflet/OpenStreetMap integration will be displayed here<br/>
        Showing {sites.length} monitoring sites across India
      </p>
      
      {/* Simulate site markers */}
      <div style={{ marginTop: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
        {sites.map((site) => (
          <div
            key={site.id}
            style={{
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              backgroundColor: getStatusColor(site.status),
              border: '2px solid white',
              boxShadow: '0 2px 4px rgba(0,0,0,0.3)',
              cursor: 'pointer'
            }}
            onClick={() => setSelectedSite(site)}
            title={site.name}
          />
        ))}
      </div>
    </div>
  );

  return (
    <React.Fragment>
      <Head title="GIS Mapping - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>GIS Mapping</BlockTitle>
              <p className="text-soft">
                Interactive map view of all monitoring sites and real-time data
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button color="primary" outline>
                  <Icon name="download"></Icon>
                  <span>Export Map</span>
                </Button>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        <Block>
          <Row className="g-gs">
            {/* Map Controls */}
            <Col lg="3">
              <Card className="card-bordered h-100">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Map Controls</h6>
                    </div>
                  </div>
                  
                  <Form>
                    <FormGroup>
                      <Label>Map Layer</Label>
                      <Input 
                        type="select" 
                        value={mapLayer}
                        onChange={(e) => setMapLayer(e.target.value)}
                      >
                        <option value="satellite">Satellite</option>
                        <option value="terrain">Terrain</option>
                        <option value="street">Street Map</option>
                        <option value="hybrid">Hybrid</option>
                      </Input>
                    </FormGroup>
                    
                    <FormGroup>
                      <Label>Display Options</Label>
                      <div className="custom-control custom-checkbox">
                        <input 
                          type="checkbox" 
                          className="custom-control-input" 
                          id="showAlerts"
                          checked={showAlerts}
                          onChange={(e) => setShowAlerts(e.target.checked)}
                        />
                        <label className="custom-control-label" htmlFor="showAlerts">
                          Show Alerts
                        </label>
                      </div>
                      <div className="custom-control custom-checkbox">
                        <input 
                          type="checkbox" 
                          className="custom-control-input" 
                          id="showEquipment"
                          checked={showEquipment}
                          onChange={(e) => setShowEquipment(e.target.checked)}
                        />
                        <label className="custom-control-label" htmlFor="showEquipment">
                          Show Equipment
                        </label>
                      </div>
                    </FormGroup>
                  </Form>

                  <div className="card-title-group mt-4">
                    <div className="card-title">
                      <h6 className="title">Legend</h6>
                    </div>
                  </div>
                  
                  <div className="nk-tb-list">
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-success" style={{ width: '20px', height: '20px' }}>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Active Sites</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-warning" style={{ width: '20px', height: '20px' }}>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Warning Sites</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-danger" style={{ width: '20px', height: '20px' }}>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Inactive Sites</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            {/* Map Display */}
            <Col lg="9">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Monitoring Sites Map</h6>
                    </div>
                    <div className="card-tools">
                      <Badge color="info">
                        {sites.filter(s => s.status === "active").length} Active Sites
                      </Badge>
                    </div>
                  </div>
                  
                  <MapPlaceholder />
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>

        {/* Site Information Panel */}
        {selectedSite && (
          <Block>
            <Card className="card-bordered">
              <CardBody className="card-inner">
                <div className="card-title-group">
                  <div className="card-title">
                    <h6 className="title">Site Information: {selectedSite.name}</h6>
                  </div>
                  <div className="card-tools">
                    <Button 
                      size="sm" 
                      color="gray" 
                      outline 
                      onClick={() => setSelectedSite(null)}
                    >
                      <Icon name="cross"></Icon>
                    </Button>
                  </div>
                </div>
                
                <Row className="g-3">
                  <Col md="3">
                    <div className="form-group">
                      <label className="form-label text-soft">Type</label>
                      <div className="form-control-wrap">
                        <Badge color="outline-primary">{selectedSite.type}</Badge>
                      </div>
                    </div>
                  </Col>
                  <Col md="3">
                    <div className="form-group">
                      <label className="form-label text-soft">Status</label>
                      <div className="form-control-wrap">
                        {getStatusBadge(selectedSite.status)}
                      </div>
                    </div>
                  </Col>
                  <Col md="3">
                    <div className="form-group">
                      <label className="form-label text-soft">Health</label>
                      <div className="form-control-wrap">
                        {getHealthBadge(selectedSite.health)}
                      </div>
                    </div>
                  </Col>
                  <Col md="3">
                    <div className="form-group">
                      <label className="form-label text-soft">Last Data</label>
                      <div className="form-control-wrap">
                        <span className="text-primary">{selectedSite.lastData}</span>
                      </div>
                    </div>
                  </Col>
                </Row>

                <Row className="g-3 mt-2">
                  <Col md="4">
                    <div className="form-group">
                      <label className="form-label text-soft">Coordinates</label>
                      <div className="form-control-wrap">
                        <span>{selectedSite.coordinates[0].toFixed(4)}°N, {selectedSite.coordinates[1].toFixed(4)}°E</span>
                      </div>
                    </div>
                  </Col>
                  <Col md="4">
                    <div className="form-group">
                      <label className="form-label text-soft">Current Reading</label>
                      <div className="form-control-wrap">
                        <span className="text-primary">
                          {selectedSite.waterLevel && `${selectedSite.waterLevel} m`}
                          {selectedSite.discharge && `${selectedSite.discharge} m³/s`}
                          {selectedSite.temperature && `${selectedSite.temperature}°C`}
                        </span>
                      </div>
                    </div>
                  </Col>
                  <Col md="4">
                    <div className="form-group">
                      <label className="form-label text-soft">Active Alerts</label>
                      <div className="form-control-wrap">
                        <Badge color={selectedSite.alerts > 0 ? "danger" : "success"}>
                          {selectedSite.alerts} Alert{selectedSite.alerts !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                    </div>
                  </Col>
                </Row>

                <div className="card-tools mt-3">
                  <ul className="btn-toolbar gx-1">
                    <li>
                      <Button size="sm" color="primary" outline>
                        <Icon name="eye"></Icon>
                        <span>View Details</span>
                      </Button>
                    </li>
                    <li>
                      <Button size="sm" color="info" outline>
                        <Icon name="growth"></Icon>
                        <span>View Data</span>
                      </Button>
                    </li>
                    <li>
                      <Button size="sm" color="warning" outline>
                        <Icon name="map-pin"></Icon>
                        <span>Navigate</span>
                      </Button>
                    </li>
                  </ul>
                </div>
              </CardBody>
            </Card>
          </Block>
        )}

        {/* Sites Summary */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Sites Summary</h6>
                </div>
              </div>
              
              <div className="nk-tb-list">
                <div className="nk-tb-item nk-tb-head">
                  <div className="nk-tb-col"><span>Site Name</span></div>
                  <div className="nk-tb-col tb-col-md"><span>Type</span></div>
                  <div className="nk-tb-col tb-col-lg"><span>Status</span></div>
                  <div className="nk-tb-col tb-col-lg"><span>Health</span></div>
                  <div className="nk-tb-col tb-col-md"><span>Last Data</span></div>
                  <div className="nk-tb-col tb-col-md"><span>Alerts</span></div>
                  <div className="nk-tb-col nk-tb-col-tools"><span>Actions</span></div>
                </div>
                
                {sites.map((site) => (
                  <div key={site.id} className="nk-tb-item">
                    <div className="nk-tb-col">
                      <div className="user-card">
                        <div className={`user-avatar bg-${site.status === 'active' ? 'success' : site.status === 'warning' ? 'warning' : 'danger'}`}>
                          <Icon name="map-pin"></Icon>
                        </div>
                        <div className="user-info">
                          <span className="tb-lead">{site.name}</span>
                          <span className="tb-sub">{site.coordinates[0].toFixed(4)}°N, {site.coordinates[1].toFixed(4)}°E</span>
                        </div>
                      </div>
                    </div>
                    <div className="nk-tb-col tb-col-md">
                      <Badge color="outline-info">{site.type}</Badge>
                    </div>
                    <div className="nk-tb-col tb-col-lg">
                      {getStatusBadge(site.status)}
                    </div>
                    <div className="nk-tb-col tb-col-lg">
                      {getHealthBadge(site.health)}
                    </div>
                    <div className="nk-tb-col tb-col-md">
                      <span className="tb-amount">{site.lastData}</span>
                    </div>
                    <div className="nk-tb-col tb-col-md">
                      <Badge color={site.alerts > 0 ? "danger" : "success"}>
                        {site.alerts}
                      </Badge>
                    </div>
                    <div className="nk-tb-col nk-tb-col-tools">
                      <ul className="nk-tb-actions gx-1">
                        <li>
                          <Button 
                            size="sm" 
                            color="primary" 
                            outline
                            onClick={() => setSelectedSite(site)}
                          >
                            <Icon name="map-pin"></Icon>
                          </Button>
                        </li>
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </Block>
      </Content>
    </React.Fragment>
  );
};

export default GISMap;
