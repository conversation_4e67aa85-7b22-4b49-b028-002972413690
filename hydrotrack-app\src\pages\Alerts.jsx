import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Button,
  Icon
} from "../components/Component";
import { Card, CardBody, Badge, Modal, ModalHeader, ModalBody, ModalFooter, Form, FormGroup, Label, Input } from "reactstrap";

const Alerts = () => {
  const [alerts, setAlerts] = useState([]);
  const [filteredAlerts, setFilteredAlerts] = useState([]);
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterPriority, setFilterPriority] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newAlert, setNewAlert] = useState({
    site: "",
    parameter: "",
    condition: "",
    threshold: "",
    priority: "medium",
    enabled: true
  });

  useEffect(() => {
    // Load sample alerts
    const sampleAlerts = [
      {
        id: 1,
        site: "Ganges - Haridwar",
        parameter: "Water Level",
        condition: "Above",
        threshold: "15.0 m",
        priority: "high",
        status: "active",
        triggered: "2024-06-11 14:30:00",
        message: "Water level exceeded critical threshold",
        acknowledged: false
      },
      {
        id: 2,
        site: "Yamuna - Delhi",
        parameter: "Discharge Rate",
        condition: "Below",
        threshold: "100 m³/s",
        priority: "medium",
        status: "active",
        triggered: "2024-06-11 13:45:00",
        message: "Discharge rate below normal range",
        acknowledged: true
      },
      {
        id: 3,
        site: "Weather Station - Mumbai",
        parameter: "Battery Level",
        condition: "Below",
        threshold: "20%",
        priority: "low",
        status: "resolved",
        triggered: "2024-06-11 12:15:00",
        message: "Low battery warning",
        acknowledged: true
      },
      {
        id: 4,
        site: "Krishna - Vijayawada",
        parameter: "Communication",
        condition: "No Data",
        threshold: "30 minutes",
        priority: "high",
        status: "active",
        triggered: "2024-06-11 11:00:00",
        message: "Communication lost with monitoring station",
        acknowledged: false
      },
      {
        id: 5,
        site: "Narmada - Bharuch",
        parameter: "Water Quality",
        condition: "Above",
        threshold: "8.5 pH",
        priority: "medium",
        status: "investigating",
        triggered: "2024-06-11 10:30:00",
        message: "pH levels above acceptable range",
        acknowledged: true
      }
    ];
    
    setAlerts(sampleAlerts);
    setFilteredAlerts(sampleAlerts);
  }, []);

  useEffect(() => {
    // Filter alerts based on status and priority
    let filtered = alerts;
    
    if (filterStatus !== "all") {
      filtered = filtered.filter(alert => alert.status === filterStatus);
    }
    
    if (filterPriority !== "all") {
      filtered = filtered.filter(alert => alert.priority === filterPriority);
    }
    
    setFilteredAlerts(filtered);
  }, [alerts, filterStatus, filterPriority]);

  const getPriorityBadge = (priority) => {
    switch (priority) {
      case "high":
        return <Badge color="danger">High</Badge>;
      case "medium":
        return <Badge color="warning">Medium</Badge>;
      case "low":
        return <Badge color="info">Low</Badge>;
      default:
        return <Badge color="secondary">Unknown</Badge>;
    }
  };

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <Badge color="danger">Active</Badge>;
      case "investigating":
        return <Badge color="warning">Investigating</Badge>;
      case "resolved":
        return <Badge color="success">Resolved</Badge>;
      default:
        return <Badge color="secondary">Unknown</Badge>;
    }
  };

  const acknowledgeAlert = (alertId) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId 
        ? { ...alert, acknowledged: true }
        : alert
    ));
  };

  const resolveAlert = (alertId) => {
    setAlerts(alerts.map(alert => 
      alert.id === alertId 
        ? { ...alert, status: "resolved", acknowledged: true }
        : alert
    ));
  };

  const handleCreateAlert = () => {
    const alert = {
      id: alerts.length + 1,
      ...newAlert,
      status: "active",
      triggered: new Date().toISOString().slice(0, 19).replace('T', ' '),
      message: `Alert rule created for ${newAlert.parameter}`,
      acknowledged: false
    };
    
    setAlerts([...alerts, alert]);
    setNewAlert({
      site: "",
      parameter: "",
      condition: "",
      threshold: "",
      priority: "medium",
      enabled: true
    });
    setShowCreateModal(false);
  };

  const alertStats = {
    total: alerts.length,
    active: alerts.filter(a => a.status === "active").length,
    unacknowledged: alerts.filter(a => !a.acknowledged).length,
    high: alerts.filter(a => a.priority === "high").length
  };

  return (
    <React.Fragment>
      <Head title="Alerts - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Alert Management</BlockTitle>
              <p className="text-soft">
                Monitor and manage system alerts and notifications
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button color="primary" onClick={() => setShowCreateModal(true)}>
                  <Icon name="plus"></Icon>
                  <span>Create Alert Rule</span>
                </Button>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        {/* Alert Statistics */}
        <Block>
          <Row className="g-gs">
            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Total Alerts</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">{alertStats.total}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-primary">
                          <Icon name="bell"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Active Alerts</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-danger">{alertStats.active}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-danger">
                          <Icon name="alert-circle"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Unacknowledged</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-warning">{alertStats.unacknowledged}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-warning">
                          <Icon name="clock"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="3" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">High Priority</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-danger">{alertStats.high}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-danger">
                          <Icon name="shield-alert"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>

        {/* Filters */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Filter Alerts</h6>
                </div>
              </div>
              <Row className="g-3">
                <Col md="4">
                  <FormGroup>
                    <Label>Status</Label>
                    <Input 
                      type="select" 
                      value={filterStatus} 
                      onChange={(e) => setFilterStatus(e.target.value)}
                    >
                      <option value="all">All Status</option>
                      <option value="active">Active</option>
                      <option value="investigating">Investigating</option>
                      <option value="resolved">Resolved</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="4">
                  <FormGroup>
                    <Label>Priority</Label>
                    <Input 
                      type="select" 
                      value={filterPriority} 
                      onChange={(e) => setFilterPriority(e.target.value)}
                    >
                      <option value="all">All Priorities</option>
                      <option value="high">High</option>
                      <option value="medium">Medium</option>
                      <option value="low">Low</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="4">
                  <FormGroup>
                    <Label>&nbsp;</Label>
                    <div>
                      <Button color="secondary" outline onClick={() => {
                        setFilterStatus("all");
                        setFilterPriority("all");
                      }}>
                        Clear Filters
                      </Button>
                    </div>
                  </FormGroup>
                </Col>
              </Row>
            </CardBody>
          </Card>
        </Block>

        {/* Alerts List */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner p-0">
              <div className="nk-tb-list">
                <div className="nk-tb-item nk-tb-head">
                  <div className="nk-tb-col"><span>Alert</span></div>
                  <div className="nk-tb-col tb-col-md"><span>Site</span></div>
                  <div className="nk-tb-col tb-col-lg"><span>Priority</span></div>
                  <div className="nk-tb-col tb-col-lg"><span>Status</span></div>
                  <div className="nk-tb-col tb-col-md"><span>Triggered</span></div>
                  <div className="nk-tb-col nk-tb-col-tools"><span>Actions</span></div>
                </div>
                
                {filteredAlerts.map((alert) => (
                  <div key={alert.id} className="nk-tb-item">
                    <div className="nk-tb-col">
                      <div className="user-card">
                        <div className={`user-avatar ${alert.acknowledged ? 'bg-light' : 'bg-warning'}`}>
                          <Icon name={alert.acknowledged ? "check" : "bell"}></Icon>
                        </div>
                        <div className="user-info">
                          <span className="tb-lead">{alert.parameter}</span>
                          <span className="tb-sub">{alert.message}</span>
                        </div>
                      </div>
                    </div>
                    <div className="nk-tb-col tb-col-md">
                      <span>{alert.site}</span>
                    </div>
                    <div className="nk-tb-col tb-col-lg">
                      {getPriorityBadge(alert.priority)}
                    </div>
                    <div className="nk-tb-col tb-col-lg">
                      {getStatusBadge(alert.status)}
                    </div>
                    <div className="nk-tb-col tb-col-md">
                      <span className="tb-amount">{alert.triggered}</span>
                    </div>
                    <div className="nk-tb-col nk-tb-col-tools">
                      <ul className="nk-tb-actions gx-1">
                        {!alert.acknowledged && (
                          <li>
                            <Button 
                              size="sm" 
                              color="warning" 
                              outline
                              onClick={() => acknowledgeAlert(alert.id)}
                            >
                              Acknowledge
                            </Button>
                          </li>
                        )}
                        {alert.status === "active" && (
                          <li>
                            <Button 
                              size="sm" 
                              color="success" 
                              outline
                              onClick={() => resolveAlert(alert.id)}
                            >
                              Resolve
                            </Button>
                          </li>
                        )}
                      </ul>
                    </div>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </Block>

        {/* Create Alert Modal */}
        <Modal isOpen={showCreateModal} toggle={() => setShowCreateModal(false)}>
          <ModalHeader toggle={() => setShowCreateModal(false)}>
            Create Alert Rule
          </ModalHeader>
          <ModalBody>
            <Form>
              <FormGroup>
                <Label>Site</Label>
                <Input 
                  type="select" 
                  value={newAlert.site}
                  onChange={(e) => setNewAlert({...newAlert, site: e.target.value})}
                >
                  <option value="">Select Site</option>
                  <option value="Ganges - Haridwar">Ganges - Haridwar</option>
                  <option value="Yamuna - Delhi">Yamuna - Delhi</option>
                  <option value="Weather Station - Mumbai">Weather Station - Mumbai</option>
                  <option value="Krishna - Vijayawada">Krishna - Vijayawada</option>
                  <option value="Narmada - Bharuch">Narmada - Bharuch</option>
                </Input>
              </FormGroup>
              
              <FormGroup>
                <Label>Parameter</Label>
                <Input 
                  type="select" 
                  value={newAlert.parameter}
                  onChange={(e) => setNewAlert({...newAlert, parameter: e.target.value})}
                >
                  <option value="">Select Parameter</option>
                  <option value="Water Level">Water Level</option>
                  <option value="Discharge Rate">Discharge Rate</option>
                  <option value="Water Quality">Water Quality</option>
                  <option value="Battery Level">Battery Level</option>
                  <option value="Communication">Communication</option>
                </Input>
              </FormGroup>
              
              <Row>
                <Col md="6">
                  <FormGroup>
                    <Label>Condition</Label>
                    <Input 
                      type="select" 
                      value={newAlert.condition}
                      onChange={(e) => setNewAlert({...newAlert, condition: e.target.value})}
                    >
                      <option value="">Select Condition</option>
                      <option value="Above">Above</option>
                      <option value="Below">Below</option>
                      <option value="Equal">Equal</option>
                      <option value="No Data">No Data</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label>Threshold</Label>
                    <Input 
                      type="text" 
                      placeholder="e.g., 15.0 m"
                      value={newAlert.threshold}
                      onChange={(e) => setNewAlert({...newAlert, threshold: e.target.value})}
                    />
                  </FormGroup>
                </Col>
              </Row>
              
              <FormGroup>
                <Label>Priority</Label>
                <Input 
                  type="select" 
                  value={newAlert.priority}
                  onChange={(e) => setNewAlert({...newAlert, priority: e.target.value})}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                </Input>
              </FormGroup>
            </Form>
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={() => setShowCreateModal(false)}>
              Cancel
            </Button>
            <Button color="primary" onClick={handleCreateAlert}>
              Create Alert
            </Button>
          </ModalFooter>
        </Modal>
      </Content>
    </React.Fragment>
  );
};

export default Alerts;
