import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Button,
  Icon
} from "../components/Component";
import { Card, CardBody, Badge, Form, FormGroup, Label, Input, Table } from "reactstrap";
import { Line, Scatter } from "react-chartjs-2";
// import DatePicker from "react-datepicker";
// import "react-datepicker/dist/react-datepicker.css";

const DataExplorer = () => {
  const [selectedSite, setSelectedSite] = useState("ganges");
  const [selectedParameter, setSelectedParameter] = useState("water_level");
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
    end: new Date()
  });
  const [chartData, setChartData] = useState(null);
  const [statistics, setStatistics] = useState(null);
  const [rawData, setRawData] = useState([]);
  const [loading, setLoading] = useState(false);

  const sites = [
    { id: "ganges", name: "Ganges - Haridwar" },
    { id: "yamuna", name: "Yamuna - Delhi" },
    { id: "mumbai", name: "Weather Station - Mumbai" },
    { id: "krishna", name: "Krishna - Vijayawada" },
    { id: "narmada", name: "Narmada - Bharuch" }
  ];

  const parameters = [
    { id: "water_level", name: "Water Level (m)", unit: "m" },
    { id: "discharge", name: "Discharge Rate (m³/s)", unit: "m³/s" },
    { id: "temperature", name: "Water Temperature (°C)", unit: "°C" },
    { id: "ph", name: "pH Level", unit: "pH" },
    { id: "turbidity", name: "Turbidity (NTU)", unit: "NTU" },
    { id: "rainfall", name: "Rainfall (mm)", unit: "mm" }
  ];

  useEffect(() => {
    fetchData();
  }, [selectedSite, selectedParameter, dateRange]);

  const fetchData = async () => {
    setLoading(true);
    
    // Simulate API call with realistic data
    setTimeout(() => {
      const days = Math.ceil((dateRange.end - dateRange.start) / (1000 * 60 * 60 * 24));
      const dataPoints = Math.min(days * 24, 168); // Max 168 points (7 days hourly)
      
      const data = [];
      const labels = [];
      
      for (let i = 0; i < dataPoints; i++) {
        const timestamp = new Date(dateRange.start.getTime() + (i * (dateRange.end - dateRange.start) / dataPoints));
        labels.push(timestamp.toLocaleDateString() + ' ' + timestamp.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
        
        let value;
        switch (selectedParameter) {
          case "water_level":
            value = 10 + Math.sin(i * 0.1) * 2 + Math.random() * 1;
            break;
          case "discharge":
            value = 150 + Math.sin(i * 0.05) * 30 + Math.random() * 20;
            break;
          case "temperature":
            value = 25 + Math.sin(i * 0.2) * 5 + Math.random() * 2;
            break;
          case "ph":
            value = 7.2 + Math.sin(i * 0.15) * 0.5 + Math.random() * 0.3;
            break;
          case "turbidity":
            value = 15 + Math.random() * 10;
            break;
          case "rainfall":
            value = Math.random() < 0.1 ? Math.random() * 20 : 0;
            break;
          default:
            value = Math.random() * 100;
        }
        
        data.push(value);
      }

      // Calculate statistics
      const sortedData = [...data].sort((a, b) => a - b);
      const stats = {
        min: Math.min(...data),
        max: Math.max(...data),
        mean: data.reduce((a, b) => a + b, 0) / data.length,
        median: sortedData[Math.floor(sortedData.length / 2)],
        stdDev: Math.sqrt(data.reduce((sq, n) => sq + Math.pow(n - (data.reduce((a, b) => a + b, 0) / data.length), 2), 0) / data.length)
      };

      setChartData({
        labels: labels,
        datasets: [
          {
            label: parameters.find(p => p.id === selectedParameter)?.name || selectedParameter,
            data: data,
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1,
            pointRadius: 1,
            pointHoverRadius: 5
          }
        ]
      });

      setStatistics(stats);
      
      // Generate raw data table
      const tableData = data.slice(-20).map((value, index) => ({
        timestamp: labels[labels.length - 20 + index],
        value: value.toFixed(2),
        quality: Math.random() > 0.1 ? "Good" : "Fair"
      }));
      
      setRawData(tableData);
      setLoading(false);
    }, 1000);
  };

  const exportData = () => {
    const csvContent = "data:text/csv;charset=utf-8," 
      + "Timestamp,Value,Quality\n"
      + rawData.map(row => `${row.timestamp},${row.value},${row.quality}`).join("\n");
    
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `${selectedSite}_${selectedParameter}_data.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: `${sites.find(s => s.id === selectedSite)?.name} - ${parameters.find(p => p.id === selectedParameter)?.name}`
      }
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time'
        },
        ticks: {
          maxTicksLimit: 10
        }
      },
      y: {
        display: true,
        title: {
          display: true,
          text: parameters.find(p => p.id === selectedParameter)?.unit || 'Value'
        }
      }
    },
    interaction: {
      intersect: false,
      mode: 'index'
    }
  };

  return (
    <React.Fragment>
      <Head title="Data Explorer - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Data Explorer</BlockTitle>
              <p className="text-soft">
                Analyze historical and real-time monitoring data
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button color="primary" outline onClick={exportData}>
                  <Icon name="download"></Icon>
                  <span>Export Data</span>
                </Button>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        {/* Data Selection Controls */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Data Selection</h6>
                </div>
              </div>
              
              <Form>
                <Row className="g-3">
                  <Col md="3">
                    <FormGroup>
                      <Label>Site</Label>
                      <Input 
                        type="select" 
                        value={selectedSite}
                        onChange={(e) => setSelectedSite(e.target.value)}
                      >
                        {sites.map(site => (
                          <option key={site.id} value={site.id}>{site.name}</option>
                        ))}
                      </Input>
                    </FormGroup>
                  </Col>
                  
                  <Col md="3">
                    <FormGroup>
                      <Label>Parameter</Label>
                      <Input 
                        type="select" 
                        value={selectedParameter}
                        onChange={(e) => setSelectedParameter(e.target.value)}
                      >
                        {parameters.map(param => (
                          <option key={param.id} value={param.id}>{param.name}</option>
                        ))}
                      </Input>
                    </FormGroup>
                  </Col>
                  
                  <Col md="3">
                    <FormGroup>
                      <Label>Start Date</Label>
                      <Input
                        type="date"
                        value={dateRange.start.toISOString().split('T')[0]}
                        onChange={(e) => setDateRange({...dateRange, start: new Date(e.target.value)})}
                      />
                    </FormGroup>
                  </Col>

                  <Col md="3">
                    <FormGroup>
                      <Label>End Date</Label>
                      <Input
                        type="date"
                        value={dateRange.end.toISOString().split('T')[0]}
                        onChange={(e) => setDateRange({...dateRange, end: new Date(e.target.value)})}
                      />
                    </FormGroup>
                  </Col>
                </Row>
                
                <Row className="g-3 mt-2">
                  <Col md="12">
                    <div className="btn-group" role="group">
                      <Button 
                        size="sm" 
                        color="outline-primary"
                        onClick={() => setDateRange({
                          start: new Date(Date.now() - 24 * 60 * 60 * 1000),
                          end: new Date()
                        })}
                      >
                        Last 24h
                      </Button>
                      <Button 
                        size="sm" 
                        color="outline-primary"
                        onClick={() => setDateRange({
                          start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                          end: new Date()
                        })}
                      >
                        Last 7 days
                      </Button>
                      <Button 
                        size="sm" 
                        color="outline-primary"
                        onClick={() => setDateRange({
                          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                          end: new Date()
                        })}
                      >
                        Last 30 days
                      </Button>
                    </div>
                  </Col>
                </Row>
              </Form>
            </CardBody>
          </Card>
        </Block>

        {/* Statistics Cards */}
        {statistics && (
          <Block>
            <Row className="g-gs">
              <Col xxl="2" md="4" sm="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-2">
                      <div className="card-title">
                        <h6 className="title">Minimum</h6>
                      </div>
                    </div>
                    <div className="align-end">
                      <div className="nk-sale-data">
                        <span className="amount h5 text-info">{statistics.min.toFixed(2)}</span>
                        <span className="unit">{parameters.find(p => p.id === selectedParameter)?.unit}</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>

              <Col xxl="2" md="4" sm="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-2">
                      <div className="card-title">
                        <h6 className="title">Maximum</h6>
                      </div>
                    </div>
                    <div className="align-end">
                      <div className="nk-sale-data">
                        <span className="amount h5 text-danger">{statistics.max.toFixed(2)}</span>
                        <span className="unit">{parameters.find(p => p.id === selectedParameter)?.unit}</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>

              <Col xxl="2" md="4" sm="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-2">
                      <div className="card-title">
                        <h6 className="title">Average</h6>
                      </div>
                    </div>
                    <div className="align-end">
                      <div className="nk-sale-data">
                        <span className="amount h5 text-primary">{statistics.mean.toFixed(2)}</span>
                        <span className="unit">{parameters.find(p => p.id === selectedParameter)?.unit}</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>

              <Col xxl="2" md="4" sm="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-2">
                      <div className="card-title">
                        <h6 className="title">Median</h6>
                      </div>
                    </div>
                    <div className="align-end">
                      <div className="nk-sale-data">
                        <span className="amount h5 text-success">{statistics.median.toFixed(2)}</span>
                        <span className="unit">{parameters.find(p => p.id === selectedParameter)?.unit}</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>

              <Col xxl="2" md="4" sm="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-2">
                      <div className="card-title">
                        <h6 className="title">Std Dev</h6>
                      </div>
                    </div>
                    <div className="align-end">
                      <div className="nk-sale-data">
                        <span className="amount h5 text-warning">{statistics.stdDev.toFixed(2)}</span>
                        <span className="unit">{parameters.find(p => p.id === selectedParameter)?.unit}</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>

              <Col xxl="2" md="4" sm="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-2">
                      <div className="card-title">
                        <h6 className="title">Data Points</h6>
                      </div>
                    </div>
                    <div className="align-end">
                      <div className="nk-sale-data">
                        <span className="amount h5">{chartData?.datasets[0]?.data?.length || 0}</span>
                        <span className="unit">points</span>
                      </div>
                    </div>
                  </CardBody>
                </Card>
              </Col>
            </Row>
          </Block>
        )}

        {/* Chart Visualization */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Time Series Visualization</h6>
                </div>
                <div className="card-tools">
                  {loading && <Badge color="warning">Loading...</Badge>}
                </div>
              </div>
              
              <div style={{ height: '400px' }}>
                {chartData && !loading && (
                  <Line 
                    data={chartData} 
                    options={{
                      ...chartOptions,
                      maintainAspectRatio: false
                    }} 
                  />
                )}
                {loading && (
                  <div className="d-flex justify-content-center align-items-center h-100">
                    <div className="spinner-border text-primary" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        </Block>

        {/* Raw Data Table */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Recent Data Points</h6>
                </div>
                <div className="card-tools">
                  <Button size="sm" color="primary" outline onClick={exportData}>
                    <Icon name="download"></Icon> Export CSV
                  </Button>
                </div>
              </div>
              
              <div className="table-responsive">
                <Table striped>
                  <thead>
                    <tr>
                      <th>Timestamp</th>
                      <th>Value ({parameters.find(p => p.id === selectedParameter)?.unit})</th>
                      <th>Quality</th>
                    </tr>
                  </thead>
                  <tbody>
                    {rawData.map((row, index) => (
                      <tr key={index}>
                        <td>{row.timestamp}</td>
                        <td>{row.value}</td>
                        <td>
                          <Badge color={row.quality === "Good" ? "success" : "warning"}>
                            {row.quality}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </div>
            </CardBody>
          </Card>
        </Block>
      </Content>
    </React.Fragment>
  );
};

export default DataExplorer;
