const { chromium } = require('playwright');
const fs = require('fs');

class SiteNavigationExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            siteNavigation: {},
            individualSites: {},
            siteFeatures: {},
            dataViews: {},
            screenshots: [],
            apis: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
        this.screenshotCounter = 1;
    }

    async init() {
        console.log('🚀 Initializing site navigation exploration...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1000
        });
        this.page = await this.browser.newPage();
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Monitor API calls for site data
        this.page.on('request', request => {
            if (request.url().includes('api') || request.url().includes('data') || request.url().includes('site')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    async takeScreenshot(name, description) {
        const filename = `screenshots/site-${String(this.screenshotCounter).padStart(2, '0')}-${name}.png`;
        await this.page.screenshot({ path: filename, fullPage: true });
        this.findings.screenshots.push({
            filename: filename,
            description: description,
            timestamp: new Date().toISOString()
        });
        this.screenshotCounter++;
        console.log(`📸 Screenshot: ${filename}`);
    }

    async login() {
        console.log('🔐 Logging in...');
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
        await this.page.waitForTimeout(3000);

        await this.page.fill('input[type="text"]', this.credentials.username);
        await this.page.fill('input[type="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        
        await this.page.waitForTimeout(8000);
        await this.takeScreenshot('login-complete', 'Dashboard after login');
        
        console.log('✅ Login successful');
        return true;
    }

    async openSidebar() {
        console.log('📱 Opening sidebar...');
        
        try {
            // Find and click menu button
            const menuButtons = await this.page.$$('button:has-text("menu"), .q-btn:has-text("menu")');
            for (const btn of menuButtons) {
                try {
                    const isVisible = await btn.isVisible();
                    if (isVisible) {
                        await btn.click();
                        await this.page.waitForTimeout(2000);
                        await this.takeScreenshot('sidebar-open', 'Sidebar navigation opened');
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (error) {
            console.log('Could not open sidebar:', error.message);
        }
        return false;
    }

    async discoverSiteNavigation() {
        console.log('🔍 Discovering site navigation structure...');

        await this.openSidebar();
        await this.page.waitForTimeout(3000);

        // First, navigate to the Sites section to see individual sites
        console.log('📍 Navigating to Sites section...');
        try {
            await this.page.click('text="listSites"');
            await this.page.waitForTimeout(5000);
            await this.takeScreenshot('sites-section', 'Sites section opened');
        } catch (e) {
            console.log('Could not click Sites directly, trying alternative...');
            try {
                const siteItems = await this.page.$$('.q-item');
                if (siteItems[1]) { // Sites is usually the second item
                    await siteItems[1].click();
                    await this.page.waitForTimeout(5000);
                }
            } catch (e2) {
                console.log('Alternative navigation failed:', e2.message);
            }
        }

        // Now discover the full navigation structure including individual sites
        const navigationStructure = await this.page.evaluate(() => {
            const structure = {
                mainSections: [],
                sites: [],
                siteSubMenus: {}
            };

            // Look for main navigation sections
            document.querySelectorAll('.q-item, .nav-item').forEach((item, index) => {
                const text = item.textContent?.trim();
                const classes = item.className;
                const hasIcon = item.querySelector('i, .q-icon');
                const iconText = hasIcon ? hasIcon.textContent?.trim() : null;

                if (text && text.length > 0) {
                    structure.mainSections.push({
                        text: text,
                        classes: classes,
                        icon: iconText,
                        index: index,
                        hasSubMenu: item.querySelector('.q-expansion-item') !== null
                    });
                }
            });

            // Look for individual sites in the sidebar (after Sites section is opened)
            document.querySelectorAll('a[href*="#"], .q-item, .site-item').forEach((item, index) => {
                const text = item.textContent?.trim();
                const href = item.getAttribute('href');

                // Identify site entries with broader patterns
                if (text && (
                    text.includes('DTPL') ||
                    text.includes('TEST') ||
                    text.includes('ENVIRO') ||
                    text.includes('Latest Data') ||
                    text.includes('Data Explorer') ||
                    text.includes('Site Info') ||
                    text.includes('Equipment') ||
                    text.includes('Notes') ||
                    text.includes('Documents') ||
                    (href && href.includes('site')) ||
                    text.match(/^[A-Z0-9_\s]+$/) // Pattern for site codes
                )) {
                    structure.sites.push({
                        text: text,
                        href: href,
                        index: index,
                        type: 'site',
                        visible: item.offsetParent !== null
                    });
                }
            });

            // Also look for sites in any expanded menus or lists
            document.querySelectorAll('.q-list .q-item, .site-list .item').forEach((item, index) => {
                const text = item.textContent?.trim();
                if (text && text.length > 0 && text.length < 100) {
                    // Check if this looks like a site name
                    if (text.match(/^[A-Z][A-Z0-9_\s]*$/) || text.includes('Site') || text.includes('Station')) {
                        structure.sites.push({
                            text: text,
                            href: item.getAttribute('href'),
                            index: index,
                            type: 'site-list-item',
                            visible: item.offsetParent !== null
                        });
                    }
                }
            });

            return structure;
        });

        // Remove duplicates
        const uniqueSites = navigationStructure.sites.filter((site, index, self) =>
            index === self.findIndex(s => s.text === site.text)
        );
        navigationStructure.sites = uniqueSites;

        this.findings.siteNavigation = navigationStructure;

        console.log(`Found ${navigationStructure.mainSections.length} main sections`);
        console.log(`Found ${navigationStructure.sites.length} individual sites:`);
        navigationStructure.sites.forEach(site => {
            console.log(`  - ${site.text} (${site.type})`);
        });

        return navigationStructure;
    }

    async exploreSite(site, siteIndex) {
        console.log(`\n🏢 Exploring site: ${site.text}`);
        
        try {
            // Ensure sidebar is open
            await this.openSidebar();
            await this.page.waitForTimeout(2000);

            // Navigate to the site
            let success = false;

            // Try multiple navigation strategies
            const strategies = [
                async () => await this.page.click(`text="${site.text}"`),
                async () => {
                    if (site.href) {
                        await this.page.goto(this.baseUrl + site.href.substring(1));
                    }
                },
                async () => {
                    const elements = await this.page.$$('.q-item');
                    if (elements[site.index]) {
                        await elements[site.index].click();
                    }
                },
                async () => {
                    // Try clicking by partial text match
                    const partialText = site.text.split(' ')[0]; // First word
                    await this.page.click(`text*="${partialText}"`);
                },
                async () => {
                    // Try finding in expanded sidebar
                    await this.openSidebar();
                    await this.page.waitForTimeout(2000);
                    await this.page.click(`text="${site.text}"`);
                }
            ];

            for (let i = 0; i < strategies.length && !success; i++) {
                try {
                    await strategies[i]();
                    success = true;
                    console.log(`✅ Navigation successful using strategy ${i + 1}`);
                } catch (e) {
                    console.log(`Strategy ${i + 1} failed:`, e.message);
                }
            }

            if (!success) {
                console.log(`❌ Could not navigate to ${site.text}`);
                return null;
            }

            // Wait for site data to load
            await this.page.waitForTimeout(5000);
            
            const safeName = site.text.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            await this.takeScreenshot(`site-${safeName}`, `Site: ${site.text}`);

            // Explore site details
            const siteData = await this.exploreSiteDetails(site.text);
            
            // Look for site-specific sub-navigation
            await this.exploreSiteSubNavigation(site.text, safeName);
            
            return siteData;

        } catch (error) {
            console.error(`❌ Error exploring site ${site.text}:`, error.message);
            return null;
        }
    }

    async exploreSiteDetails(siteName) {
        console.log(`📊 Analyzing site details for: ${siteName}`);
        
        const siteDetails = await this.page.evaluate(() => {
            const details = {
                url: window.location.href,
                title: document.title,
                dataElements: {
                    tables: [],
                    charts: [],
                    maps: [],
                    widgets: [],
                    forms: [],
                    buttons: [],
                    dataDisplays: []
                },
                siteInfo: {},
                realTimeData: {},
                historicalData: {}
            };

            // Analyze tables (data grids)
            document.querySelectorAll('table, .q-table, .data-table, .grid').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th, .header')).map(th => th.textContent.trim());
                const rows = table.querySelectorAll('tr, .row').length;
                const data = Array.from(table.querySelectorAll('td')).slice(0, 20).map(td => td.textContent.trim());
                
                details.dataElements.tables.push({
                    id: table.id || `table-${index}`,
                    headers: headers,
                    rowCount: rows,
                    sampleData: data,
                    classes: table.className
                });
            });

            // Analyze charts and graphs
            document.querySelectorAll('canvas, svg, .chart, .graph, .plot').forEach((chart, index) => {
                details.dataElements.charts.push({
                    type: chart.tagName,
                    id: chart.id || `chart-${index}`,
                    classes: chart.className,
                    width: chart.width || chart.clientWidth,
                    height: chart.height || chart.clientHeight,
                    parentClasses: chart.parentElement?.className
                });
            });

            // Analyze maps
            document.querySelectorAll('.map, #map, .leaflet-container, .ol-viewport').forEach((map, index) => {
                details.dataElements.maps.push({
                    id: map.id || `map-${index}`,
                    classes: map.className,
                    width: map.clientWidth,
                    height: map.clientHeight
                });
            });

            // Analyze widgets/cards
            document.querySelectorAll('.q-card, .widget, .dashboard-widget, .data-widget').forEach((widget, index) => {
                const title = widget.querySelector('h1, h2, h3, h4, h5, h6, .title')?.textContent?.trim();
                const content = widget.textContent.trim().substring(0, 200);
                
                details.dataElements.widgets.push({
                    id: widget.id || `widget-${index}`,
                    title: title,
                    content: content,
                    classes: widget.className
                });
            });

            // Analyze buttons and actions
            document.querySelectorAll('button, .q-btn, input[type="button"]').forEach((btn, index) => {
                const text = btn.textContent?.trim() || btn.value;
                if (text && text.length < 100) {
                    details.dataElements.buttons.push({
                        text: text,
                        type: btn.type,
                        classes: btn.className,
                        disabled: btn.disabled,
                        id: btn.id || `button-${index}`
                    });
                }
            });

            // Look for site information panels
            document.querySelectorAll('.site-info, .info-panel, .details').forEach(panel => {
                const text = panel.textContent.trim();
                if (text) {
                    details.siteInfo.description = text.substring(0, 500);
                }
            });

            // Look for real-time data indicators
            document.querySelectorAll('.real-time, .live-data, .current').forEach(element => {
                const text = element.textContent.trim();
                if (text) {
                    details.realTimeData.indicators = details.realTimeData.indicators || [];
                    details.realTimeData.indicators.push(text);
                }
            });

            return details;
        });

        this.findings.individualSites[siteName] = siteDetails;
        
        console.log(`✅ Site analysis complete: ${siteDetails.dataElements.tables.length} tables, ${siteDetails.dataElements.charts.length} charts, ${siteDetails.dataElements.widgets.length} widgets`);
        
        return siteDetails;
    }

    async exploreSiteSubNavigation(siteName, safeName) {
        console.log(`🔍 Looking for sub-navigation in site: ${siteName}`);
        
        // Look for tabs, sub-menus, or data views within the site
        const subNavigation = await this.page.evaluate(() => {
            const subNav = {
                tabs: [],
                dataViews: [],
                actionButtons: [],
                filters: []
            };

            // Find tabs
            document.querySelectorAll('.q-tab, .tab, [role="tab"]').forEach(tab => {
                const text = tab.textContent.trim();
                if (text) {
                    subNav.tabs.push({
                        text: text,
                        active: tab.classList.contains('active') || tab.classList.contains('q-tab--active')
                    });
                }
            });

            // Find data view options
            document.querySelectorAll('.data-view, .view-option, .display-option').forEach(view => {
                const text = view.textContent.trim();
                if (text) {
                    subNav.dataViews.push(text);
                }
            });

            // Find action buttons specific to this site
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent.trim();
                if (text && text.length < 50 && 
                    (text.includes('View') || text.includes('Data') || text.includes('Export') || 
                     text.includes('Report') || text.includes('Chart') || text.includes('Graph'))) {
                    subNav.actionButtons.push(text);
                }
            });

            // Find filters or controls
            document.querySelectorAll('select, .filter, .control').forEach(control => {
                const label = control.getAttribute('aria-label') || control.previousElementSibling?.textContent?.trim();
                if (label) {
                    subNav.filters.push(label);
                }
            });

            return subNav;
        });

        if (subNavigation.tabs.length > 0) {
            console.log(`  Found ${subNavigation.tabs.length} tabs: ${subNavigation.tabs.map(t => t.text).join(', ')}`);
            
            // Try clicking on different tabs to see different views
            for (let i = 0; i < Math.min(subNavigation.tabs.length, 3); i++) {
                const tab = subNavigation.tabs[i];
                try {
                    console.log(`    Exploring tab: ${tab.text}`);
                    await this.page.click(`text="${tab.text}"`);
                    await this.page.waitForTimeout(3000);
                    
                    await this.takeScreenshot(`${safeName}-tab-${tab.text.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()}`, 
                                            `${siteName} - ${tab.text} tab`);
                    
                    // Analyze this tab's content
                    const tabContent = await this.exploreSiteDetails(`${siteName} - ${tab.text}`);
                    this.findings.siteFeatures[`${siteName}_${tab.text}`] = tabContent;
                    
                } catch (e) {
                    console.log(`    Could not explore tab ${tab.text}:`, e.message);
                }
            }
        }

        if (subNavigation.actionButtons.length > 0) {
            console.log(`  Found action buttons: ${subNavigation.actionButtons.join(', ')}`);
        }

        this.findings.dataViews[siteName] = subNavigation;
    }

    async generateSiteNavigationReport() {
        console.log('📊 Generating comprehensive site navigation report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            exploration: {
                totalSites: Object.keys(this.findings.individualSites).length,
                totalScreenshots: this.findings.screenshots.length,
                totalAPIs: this.findings.apis.length
            },
            siteNavigation: this.findings.siteNavigation,
            individualSites: this.findings.individualSites,
            siteFeatures: this.findings.siteFeatures,
            dataViews: this.findings.dataViews,
            screenshots: this.findings.screenshots,
            apiCalls: this.findings.apis,
            summary: {
                sitesExplored: Object.keys(this.findings.individualSites),
                featuresFound: Object.keys(this.findings.siteFeatures),
                screenshotsCaptured: this.findings.screenshots.map(s => s.filename)
            }
        };

        fs.writeFileSync('site-navigation-report.json', JSON.stringify(report, null, 2));
        console.log('✅ Site navigation report saved');
        return report;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new SiteNavigationExplorer();
    
    try {
        // Create screenshots directory
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        await explorer.init();
        await explorer.login();
        
        // Discover site navigation structure
        const navigation = await explorer.discoverSiteNavigation();
        
        // Explore each individual site in detail
        for (let i = 0; i < navigation.sites.length; i++) {
            const site = navigation.sites[i];
            console.log(`\n--- Exploring Site ${i + 1}/${navigation.sites.length} ---`);
            await explorer.exploreSite(site, i);
            await explorer.page.waitForTimeout(2000); // Brief pause between sites
        }
        
        const report = await explorer.generateSiteNavigationReport();
        
        console.log('\n🎉 Site navigation exploration completed!');
        console.log('📊 Summary:');
        console.log(`  - Sites explored: ${report.exploration.totalSites}`);
        console.log(`  - Screenshots taken: ${report.exploration.totalScreenshots}`);
        console.log(`  - API calls captured: ${report.exploration.totalAPIs}`);
        console.log(`  - Sites found: ${report.summary.sitesExplored.join(', ')}`);
        
    } catch (error) {
        console.error('❌ Site navigation exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = SiteNavigationExplorer;
