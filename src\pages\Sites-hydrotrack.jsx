import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Card,
  CardBody,
  Badge,
  Button,
  Icon,
  DataTable,
  DataTableBody,
  DataTableHead,
  DataTableRow,
  DataTableItem,
  PaginationComponent,
  RSelect
} from "../components/Component";

const Sites = () => {
  const [sites, setSites] = useState([]);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemPerPage] = useState(10);
  const [filterStatus, setFilterStatus] = useState({ value: "all", label: "All Status" });
  const [filterType, setFilterType] = useState({ value: "all", label: "All Types" });

  // Sample site data
  const sampleSites = [
    {
      id: 1,
      name: "River Ganges - <PERSON>dwar",
      type: "Water Level",
      location: "Haridwar, Uttarakhand",
      status: "Active",
      lastData: "2 min ago",
      health: "Good",
      coordinates: "29.9457°N, 78.1642°E",
      equipment: ["Logger", "Water Level Sensor", "Temperature Sensor"]
    },
    {
      id: 2,
      name: "Yamuna Discharge - Delhi",
      type: "Discharge",
      location: "Delhi",
      status: "Active",
      lastData: "5 min ago",
      health: "Warning",
      coordinates: "28.7041°N, 77.1025°E",
      equipment: ["Flow Meter", "Pressure Sensor"]
    },
    {
      id: 3,
      name: "Weather Station - Mumbai",
      type: "Weather",
      location: "Mumbai, Maharashtra",
      status: "Active",
      lastData: "1 min ago",
      health: "Good",
      coordinates: "19.0760°N, 72.8777°E",
      equipment: ["Rain Gauge", "Wind Sensor", "Temperature Sensor"]
    },
    {
      id: 4,
      name: "Krishna River - Vijayawada",
      type: "Water Level",
      location: "Vijayawada, Andhra Pradesh",
      status: "Inactive",
      lastData: "2 hours ago",
      health: "Poor",
      coordinates: "16.5062°N, 80.6480°E",
      equipment: ["Logger", "Water Level Sensor"]
    },
    {
      id: 5,
      name: "Narmada Discharge - Bharuch",
      type: "Discharge",
      location: "Bharuch, Gujarat",
      status: "Active",
      lastData: "3 min ago",
      health: "Good",
      coordinates: "21.7051°N, 72.9959°E",
      equipment: ["Flow Meter", "Velocity Sensor", "Depth Sensor"]
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setSites(sampleSites);
      setLoading(false);
    }, 1000);
  }, []);

  const statusOptions = [
    { value: "all", label: "All Status" },
    { value: "active", label: "Active" },
    { value: "inactive", label: "Inactive" },
    { value: "maintenance", label: "Maintenance" }
  ];

  const typeOptions = [
    { value: "all", label: "All Types" },
    { value: "water-level", label: "Water Level" },
    { value: "discharge", label: "Discharge" },
    { value: "weather", label: "Weather" }
  ];

  const getStatusBadge = (status) => {
    switch (status.toLowerCase()) {
      case "active":
        return <Badge color="success">Active</Badge>;
      case "inactive":
        return <Badge color="danger">Inactive</Badge>;
      case "maintenance":
        return <Badge color="warning">Maintenance</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  const getHealthBadge = (health) => {
    switch (health.toLowerCase()) {
      case "good":
        return <Badge color="success">Good</Badge>;
      case "warning":
        return <Badge color="warning">Warning</Badge>;
      case "poor":
        return <Badge color="danger">Poor</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  const filteredSites = sites.filter(site => {
    const statusMatch = filterStatus.value === "all" || 
                       site.status.toLowerCase() === filterStatus.value;
    const typeMatch = filterType.value === "all" || 
                     site.type.toLowerCase().replace(" ", "-") === filterType.value;
    return statusMatch && typeMatch;
  });

  // Pagination
  const indexOfLastItem = currentPage * itemPerPage;
  const indexOfFirstItem = indexOfLastItem - itemPerPage;
  const currentItems = filteredSites.slice(indexOfFirstItem, indexOfLastItem);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  return (
    <React.Fragment>
      <Head title="Sites - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Site Management</BlockTitle>
              <p className="text-soft">
                Manage and monitor all water monitoring sites
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button
                  className="btn-icon btn-trigger toggle-expand me-n1"
                  onClick={() => {}}
                >
                  <Icon name="menu-alt-r"></Icon>
                </Button>
                <div className="toggle-expand-content">
                  <ul className="nk-block-tools g-3">
                    <li>
                      <Button color="primary">
                        <Icon name="plus"></Icon>
                        <span>Add Site</span>
                      </Button>
                    </li>
                  </ul>
                </div>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        <Block>
          <Card className="card-bordered card-preview">
            <CardBody>
              <div className="card-inner-group">
                <div className="card-inner position-relative card-tools-toggle">
                  <div className="card-title-group">
                    <div className="card-tools">
                      <div className="form-inline flex-nowrap gx-3">
                        <div className="form-wrap w-150px">
                          <RSelect
                            options={statusOptions}
                            value={filterStatus}
                            onChange={setFilterStatus}
                            placeholder="Filter by Status"
                          />
                        </div>
                        <div className="form-wrap w-150px">
                          <RSelect
                            options={typeOptions}
                            value={filterType}
                            onChange={setFilterType}
                            placeholder="Filter by Type"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="card-inner p-0">
                  <DataTable className="nk-tb-list nk-tb-ulist">
                    <DataTableHead className="nk-tb-item nk-tb-head">
                      <DataTableRow>
                        <span className="nk-tb-col">
                          <span className="sub-text">Site Name</span>
                        </span>
                        <span className="nk-tb-col tb-col-mb">
                          <span className="sub-text">Type</span>
                        </span>
                        <span className="nk-tb-col tb-col-md">
                          <span className="sub-text">Location</span>
                        </span>
                        <span className="nk-tb-col tb-col-lg">
                          <span className="sub-text">Status</span>
                        </span>
                        <span className="nk-tb-col tb-col-lg">
                          <span className="sub-text">Health</span>
                        </span>
                        <span className="nk-tb-col tb-col-md">
                          <span className="sub-text">Last Data</span>
                        </span>
                        <span className="nk-tb-col nk-tb-col-tools text-end">
                          <span className="sub-text">Actions</span>
                        </span>
                      </DataTableRow>
                    </DataTableHead>
                    <DataTableBody>
                      {loading ? (
                        <DataTableRow>
                          <DataTableItem colSpan="7">
                            <div className="text-center">Loading...</div>
                          </DataTableItem>
                        </DataTableRow>
                      ) : currentItems.length > 0 ? (
                        currentItems.map((site) => (
                          <DataTableRow key={site.id}>
                            <DataTableItem>
                              <div className="user-card">
                                <div className="user-avatar bg-primary">
                                  <Icon name="map-pin"></Icon>
                                </div>
                                <div className="user-info">
                                  <span className="tb-lead">{site.name}</span>
                                  <span className="tb-sub">{site.coordinates}</span>
                                </div>
                              </div>
                            </DataTableItem>
                            <DataTableItem className="tb-col-mb">
                              <Badge color="outline-info">{site.type}</Badge>
                            </DataTableItem>
                            <DataTableItem className="tb-col-md">
                              <span>{site.location}</span>
                            </DataTableItem>
                            <DataTableItem className="tb-col-lg">
                              {getStatusBadge(site.status)}
                            </DataTableItem>
                            <DataTableItem className="tb-col-lg">
                              {getHealthBadge(site.health)}
                            </DataTableItem>
                            <DataTableItem className="tb-col-md">
                              <span className="tb-amount">{site.lastData}</span>
                            </DataTableItem>
                            <DataTableItem className="nk-tb-col-tools">
                              <ul className="nk-tb-actions gx-1">
                                <li>
                                  <Button size="sm" color="trigger" className="toggle">
                                    <Icon name="eye"></Icon>
                                  </Button>
                                </li>
                                <li>
                                  <Button size="sm" color="trigger" className="toggle">
                                    <Icon name="edit"></Icon>
                                  </Button>
                                </li>
                                <li>
                                  <Button size="sm" color="trigger" className="toggle">
                                    <Icon name="more-h"></Icon>
                                  </Button>
                                </li>
                              </ul>
                            </DataTableItem>
                          </DataTableRow>
                        ))
                      ) : (
                        <DataTableRow>
                          <DataTableItem colSpan="7">
                            <div className="text-center">No sites found</div>
                          </DataTableItem>
                        </DataTableRow>
                      )}
                    </DataTableBody>
                  </DataTable>
                </div>
                <div className="card-inner">
                  <PaginationComponent
                    itemPerPage={itemPerPage}
                    totalItems={filteredSites.length}
                    paginate={paginate}
                    currentPage={currentPage}
                  />
                </div>
              </div>
            </CardBody>
          </Card>
        </Block>
      </Content>
    </React.Fragment>
  );
};

export default Sites;
