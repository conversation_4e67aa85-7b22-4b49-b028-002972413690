{"sitesTable": {"description": "Main sites table grid accessible from sidebar", "headers": ["Site name / Site description", "Group", "Station ID / Project ID", "Logger ID / Firmware version", "Current scan interval / Default scan interval", "Last report", "Health / Wireless signals", "Actions"], "totalSites": 6, "siteEntries": [{"name": "DTPL Test Water Level", "group": "DTPLENVIRO", "loggerId": "00100245", "firmwareVersion": "1.1.1", "scanInterval": "5 min", "lastReport": "2025-05-24 18:26:15", "connectionType": "GPRS"}, {"name": "DTPL TEST Water Level", "group": "DTPLENVIRO", "loggerId": "00100276", "firmwareVersion": "1.1.0", "scanInterval": "5 min", "lastReport": "2025-05-08 18:00:46", "connectionType": "GPRS"}, {"name": "DTPLENVIRO 30170 WRD DEMO", "group": "DTPLENVIRO", "loggerId": "30170", "firmwareVersion": "4.1.6", "scanInterval": "30 min", "lastReport": "2025-06-10 14:30:53", "connectionType": "GPRS"}, {"name": "DTPLTEST Water Level", "group": "DTPLENVIRO", "loggerId": "00100278", "firmwareVersion": "1.1.0", "scanInterval": "5 min", "lastReport": "2025-05-02 12:30:51", "connectionType": "GPRS"}, {"name": "DTPLTEST Water level", "group": "DTPLENVIRO", "loggerId": "00100277", "firmwareVersion": "Not specified", "scanInterval": "5 min", "lastReport": "2025-05-08 18:00:46", "connectionType": "GPRS"}, {"name": "UJVNL-<PERSON><PERSON><PERSON>am Discharge", "group": "DTPLENVIRO", "loggerId": "00100286", "firmwareVersion": "1.1.0", "scanInterval": "15 min", "lastReport": "2025-06-10 14:45:43", "connectionType": "GPRS"}]}, "siteTabStructure": {"description": "When clicking on individual site, multiple tabs become available", "tabs": [{"name": "Latest Data", "icon": "speed", "description": "Real-time data from monitoring equipment", "features": ["Live measurements display", "Current sensor readings", "Real-time status indicators", "Data tables with current values", "Timestamp information"]}, {"name": "Data Explorer", "icon": "bar_chart", "description": "Graphs and charts for each parameter", "features": ["Parameter-specific graphs", "Historical data visualization", "Interactive charts", "Time-series analysis", "Multiple parameter comparison", "Customizable date ranges"]}, {"name": "Site Info", "icon": "info", "description": "Detailed site information and configuration", "features": ["Site location details", "Equipment specifications", "Installation information", "Site metadata", "Configuration parameters"]}, {"name": "Equipment & Data", "icon": "settings", "description": "Equipment status and data configuration", "features": ["Equipment health monitoring", "Sensor status indicators", "Data collection settings", "Communication status", "Maintenance information"]}, {"name": "Notes", "icon": "sticky_note_2", "description": "Site notes and documentation management", "features": ["Site-specific notes", "Maintenance logs", "Observation records", "User annotations", "Historical notes"]}, {"name": "Documents", "icon": "attach_file", "description": "Document management for site files", "features": ["File upload/download", "Document storage", "Installation manuals", "Calibration certificates", "Site documentation"]}]}, "apiEndpoints": [{"endpoint": "/api/v1/sites/get", "method": "GET", "description": "Get basic sites list"}, {"endpoint": "/api/v1/sites/get_verbose?health=true", "method": "GET", "description": "Get detailed sites information with health status"}, {"endpoint": "/api/v1/aggregate", "method": "POST", "description": "Aggregate data queries for site information"}], "workflowSteps": [{"step": 1, "action": "Login to Hydroview Platform", "description": "Authenticate using username and password with RSA encryption"}, {"step": 2, "action": "Open Sidebar Navigation", "description": "Click hamburger menu to access main navigation"}, {"step": 3, "action": "Navigate to Sites Section", "description": "Click on 'Sites' in the sidebar to access sites management"}, {"step": 4, "action": "View Sites Table Grid", "description": "See comprehensive table with all monitoring sites and their status"}, {"step": 5, "action": "Select Individual Site", "description": "Click on specific site name to access detailed site view"}, {"step": 6, "action": "Explore Site Tabs", "description": "Navigate through multiple tabs for different site functionalities"}], "technicalDetails": {"framework": "Quasar Framework (Vue.js)", "tableComponent": "q-table with sortable columns", "navigationPattern": "Sidebar with collapsible menu", "dataRefresh": "Real-time updates via API polling", "authentication": "RSA encryption + Bearer tokens", "responsiveDesign": "Mobile and desktop optimized"}}