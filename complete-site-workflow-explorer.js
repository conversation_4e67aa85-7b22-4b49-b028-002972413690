const { chromium } = require('playwright');
const fs = require('fs');

class CompleteSiteWorkflowExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            sitesTable: {},
            individualSites: {},
            siteTabsData: {},
            parameterGraphs: {},
            screenshots: [],
            apis: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
        this.screenshotCounter = 1;
    }

    async init() {
        console.log('🚀 Initializing complete site workflow exploration...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1500
        });
        this.page = await this.browser.newPage();
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Monitor API calls
        this.page.on('request', request => {
            if (request.url().includes('api') || request.url().includes('data') || request.url().includes('site')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    async takeScreenshot(name, description) {
        const filename = `screenshots/workflow-${String(this.screenshotCounter).padStart(2, '0')}-${name}.png`;
        await this.page.screenshot({ path: filename, fullPage: true });
        this.findings.screenshots.push({
            filename: filename,
            description: description,
            timestamp: new Date().toISOString()
        });
        this.screenshotCounter++;
        console.log(`📸 ${filename} - ${description}`);
    }

    async login() {
        console.log('🔐 Logging in...');
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
        await this.page.waitForTimeout(3000);

        await this.page.fill('input[type="text"]', this.credentials.username);
        await this.page.fill('input[type="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        
        await this.page.waitForTimeout(8000);
        await this.takeScreenshot('login-complete', 'Dashboard after login');
        
        console.log('✅ Login successful');
        return true;
    }

    async openSidebar() {
        console.log('📱 Opening sidebar...');
        try {
            const menuButtons = await this.page.$$('button:has-text("menu"), .q-btn:has-text("menu")');
            for (const btn of menuButtons) {
                try {
                    const isVisible = await btn.isVisible();
                    if (isVisible) {
                        await btn.click();
                        await this.page.waitForTimeout(2000);
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (error) {
            console.log('Could not open sidebar:', error.message);
        }
        return false;
    }

    async navigateToSitesSection() {
        console.log('🏢 Navigating to Sites section...');

        await this.openSidebar();
        await this.page.waitForTimeout(3000);
        await this.takeScreenshot('sidebar-opened', 'Sidebar opened');

        try {
            // Enhanced strategies to find and click Sites
            const strategies = [
                // Strategy 1: Direct text click
                async () => {
                    await this.page.click('text="listSites"');
                },
                // Strategy 2: Click by icon and text combination
                async () => {
                    await this.page.click('.q-item:has-text("Sites")');
                },
                // Strategy 3: Click the second q-item (Sites is usually second)
                async () => {
                    const siteItems = await this.page.$$('.q-item');
                    if (siteItems[1]) {
                        await siteItems[1].click();
                    }
                },
                // Strategy 4: Find by icon
                async () => {
                    await this.page.click('.q-item:has(.q-icon:has-text("list"))');
                },
                // Strategy 5: Navigate by URL hash
                async () => {
                    await this.page.goto(this.baseUrl + 'sites');
                },
                // Strategy 6: Force click on any element containing "Sites"
                async () => {
                    const elements = await this.page.$$('*');
                    for (const element of elements) {
                        try {
                            const text = await element.textContent();
                            if (text && text.includes('Sites') && text.length < 50) {
                                const isVisible = await element.isVisible();
                                if (isVisible) {
                                    await element.click();
                                    break;
                                }
                            }
                        } catch (e) {
                            continue;
                        }
                    }
                }
            ];

            for (let i = 0; i < strategies.length; i++) {
                try {
                    console.log(`Trying strategy ${i + 1}...`);
                    await strategies[i]();
                    await this.page.waitForTimeout(5000);

                    // Check if we successfully navigated by looking for table or site content
                    const hasTable = await this.page.$('table, .q-table, .data-table');
                    const hasSiteContent = await this.page.$('.site-item, .site-list');

                    if (hasTable || hasSiteContent) {
                        await this.takeScreenshot('sites-section', 'Sites section with table grid');
                        console.log('✅ Successfully navigated to Sites section');
                        return true;
                    }
                } catch (e) {
                    console.log(`Strategy ${i + 1} failed:`, e.message);
                }
            }

            // If all strategies fail, try to find any table on the current page
            console.log('All strategies failed, checking current page for tables...');
            const currentTables = await this.page.$$('table, .q-table');
            if (currentTables.length > 0) {
                await this.takeScreenshot('current-page-with-table', 'Current page with table found');
                console.log('✅ Found table on current page, proceeding...');
                return true;
            }

        } catch (error) {
            console.error('❌ Failed to navigate to Sites section:', error.message);
        }
        return false;
    }

    async exploreSitesTable() {
        console.log('📊 Exploring sites table grid...');
        
        const sitesTableData = await this.page.evaluate(() => {
            const tableData = {
                headers: [],
                rows: [],
                siteNames: [],
                tableStructure: {}
            };

            // Find the main table
            const tables = document.querySelectorAll('table, .q-table, .data-table, .grid');
            
            tables.forEach((table, tableIndex) => {
                // Get headers
                const headers = Array.from(table.querySelectorAll('th, .header, .column-header')).map(th => th.textContent.trim());
                
                // Get rows
                const rows = Array.from(table.querySelectorAll('tr')).slice(1); // Skip header row
                const rowData = rows.map(row => {
                    const cells = Array.from(row.querySelectorAll('td, .cell')).map(cell => cell.textContent.trim());
                    return cells;
                });

                // Look for clickable site names
                const clickableSites = Array.from(table.querySelectorAll('a, .clickable, .site-link, .q-btn')).map(link => ({
                    text: link.textContent.trim(),
                    href: link.getAttribute('href'),
                    classes: link.className
                }));

                if (headers.length > 0 || rowData.length > 0) {
                    tableData.headers = headers;
                    tableData.rows = rowData;
                    tableData.siteNames = clickableSites.filter(site => site.text.length > 0);
                    tableData.tableStructure = {
                        totalRows: rowData.length,
                        totalColumns: headers.length,
                        tableIndex: tableIndex
                    };
                }
            });

            // Also look for site names in lists or cards
            document.querySelectorAll('.site-item, .q-card, .item').forEach(item => {
                const text = item.textContent.trim();
                if (text && text.length < 100 && (text.includes('DTPL') || text.includes('Site') || text.includes('TEST'))) {
                    tableData.siteNames.push({
                        text: text,
                        classes: item.className,
                        clickable: true
                    });
                }
            });

            return tableData;
        });

        this.findings.sitesTable = sitesTableData;
        
        console.log(`✅ Sites table analyzed:`);
        console.log(`  - Headers: ${sitesTableData.headers.join(', ')}`);
        console.log(`  - Rows: ${sitesTableData.tableStructure.totalRows}`);
        console.log(`  - Clickable sites found: ${sitesTableData.siteNames.length}`);
        
        sitesTableData.siteNames.forEach(site => {
            console.log(`    - ${site.text}`);
        });

        return sitesTableData.siteNames;
    }

    async exploreIndividualSite(siteName, siteIndex) {
        console.log(`\n🏭 Exploring individual site: ${siteName}`);
        
        try {
            // Click on the site
            await this.page.click(`text="${siteName}"`);
            await this.page.waitForTimeout(5000);
            
            const safeName = siteName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            await this.takeScreenshot(`site-${safeName}`, `Individual site: ${siteName}`);

            // Discover tabs for this site
            const tabs = await this.discoverSiteTabs();
            
            // Explore each tab
            for (let i = 0; i < tabs.length; i++) {
                const tab = tabs[i];
                await this.exploreSiteTab(siteName, tab, i);
            }

            return true;
        } catch (error) {
            console.error(`❌ Error exploring site ${siteName}:`, error.message);
            return false;
        }
    }

    async discoverSiteTabs() {
        console.log('🔍 Discovering site tabs...');
        
        const tabs = await this.page.evaluate(() => {
            const tabElements = [];
            
            // Look for tabs
            document.querySelectorAll('.q-tab, .tab, [role="tab"], .nav-tab').forEach(tab => {
                const text = tab.textContent.trim();
                if (text) {
                    tabElements.push({
                        text: text,
                        active: tab.classList.contains('active') || tab.classList.contains('q-tab--active'),
                        classes: tab.className
                    });
                }
            });

            // Also look for tab-like buttons
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent.trim();
                if (text && (
                    text.includes('Latest Data') ||
                    text.includes('Data Explorer') ||
                    text.includes('Site Info') ||
                    text.includes('Equipment') ||
                    text.includes('Notes') ||
                    text.includes('Documents') ||
                    text.includes('Graphs')
                )) {
                    tabElements.push({
                        text: text,
                        active: false,
                        classes: btn.className,
                        type: 'button'
                    });
                }
            });

            return tabElements;
        });

        console.log(`Found ${tabs.length} tabs:`);
        tabs.forEach(tab => {
            console.log(`  - ${tab.text} ${tab.active ? '(active)' : ''}`);
        });

        return tabs;
    }

    async exploreSiteTab(siteName, tab, tabIndex) {
        console.log(`📋 Exploring tab: ${tab.text}`);
        
        try {
            // Click on the tab
            await this.page.click(`text="${tab.text}"`);
            await this.page.waitForTimeout(4000);
            
            const safeSiteName = siteName.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            const safeTabName = tab.text.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            await this.takeScreenshot(`${safeSiteName}-tab-${safeTabName}`, `${siteName} - ${tab.text} tab`);

            // Analyze tab content
            const tabContent = await this.analyzeTabContent(tab.text);
            
            // Store findings
            const key = `${siteName}_${tab.text}`;
            this.findings.siteTabsData[key] = tabContent;

            // Special handling for Data Explorer (parameter graphs)
            if (tab.text.includes('Data Explorer') || tab.text.includes('Graphs')) {
                await this.exploreParameterGraphs(siteName, safeTabName);
            }

            console.log(`✅ Tab analyzed: ${tabContent.charts.length} charts, ${tabContent.tables.length} tables, ${tabContent.widgets.length} widgets`);

        } catch (error) {
            console.error(`❌ Error exploring tab ${tab.text}:`, error.message);
        }
    }

    async analyzeTabContent(tabName) {
        console.log(`🔍 Analyzing content for tab: ${tabName}`);
        
        const content = await this.page.evaluate(() => {
            const analysis = {
                charts: [],
                tables: [],
                widgets: [],
                parameters: [],
                buttons: [],
                forms: [],
                dataPoints: []
            };

            // Analyze charts and graphs
            document.querySelectorAll('canvas, svg, .chart, .graph, .plot').forEach((chart, index) => {
                analysis.charts.push({
                    type: chart.tagName,
                    id: chart.id || `chart-${index}`,
                    classes: chart.className,
                    width: chart.width || chart.clientWidth,
                    height: chart.height || chart.clientHeight,
                    parentText: chart.parentElement?.textContent?.trim().substring(0, 100)
                });
            });

            // Analyze tables
            document.querySelectorAll('table, .q-table, .data-table').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
                const rows = table.querySelectorAll('tr').length;
                const sampleData = Array.from(table.querySelectorAll('td')).slice(0, 10).map(td => td.textContent.trim());
                
                analysis.tables.push({
                    id: table.id || `table-${index}`,
                    headers: headers,
                    rowCount: rows,
                    sampleData: sampleData,
                    classes: table.className
                });
            });

            // Look for parameter names and values
            document.querySelectorAll('.parameter, .metric, .value, .measurement').forEach(param => {
                const text = param.textContent.trim();
                if (text && text.length < 200) {
                    analysis.parameters.push(text);
                }
            });

            // Look for data points and values
            document.querySelectorAll('.data-point, .value, .reading').forEach(point => {
                const text = point.textContent.trim();
                if (text && text.match(/\d+/)) { // Contains numbers
                    analysis.dataPoints.push(text);
                }
            });

            // Analyze widgets/cards
            document.querySelectorAll('.q-card, .widget, .panel').forEach((widget, index) => {
                const title = widget.querySelector('h1, h2, h3, h4, h5, h6, .title')?.textContent?.trim();
                const content = widget.textContent.trim().substring(0, 200);
                
                analysis.widgets.push({
                    id: widget.id || `widget-${index}`,
                    title: title,
                    content: content,
                    classes: widget.className
                });
            });

            // Analyze buttons and actions
            document.querySelectorAll('button, .q-btn').forEach((btn, index) => {
                const text = btn.textContent?.trim();
                if (text && text.length < 100) {
                    analysis.buttons.push({
                        text: text,
                        type: btn.type,
                        classes: btn.className,
                        disabled: btn.disabled
                    });
                }
            });

            return analysis;
        });

        return content;
    }

    async exploreParameterGraphs(siteName, tabName) {
        console.log(`📈 Exploring parameter graphs in Data Explorer...`);
        
        // Look for different parameter options or graph types
        const parameterOptions = await this.page.evaluate(() => {
            const options = [];
            
            // Look for parameter selectors, dropdowns, or buttons
            document.querySelectorAll('select option, .parameter-option, .graph-option').forEach(option => {
                const text = option.textContent.trim();
                if (text) {
                    options.push(text);
                }
            });

            // Look for parameter names in labels or headings
            document.querySelectorAll('label, .parameter-label, h1, h2, h3, h4, h5, h6').forEach(label => {
                const text = label.textContent.trim();
                if (text && text.length < 100 && (
                    text.includes('Temperature') ||
                    text.includes('Pressure') ||
                    text.includes('Flow') ||
                    text.includes('Level') ||
                    text.includes('Speed') ||
                    text.includes('Voltage') ||
                    text.includes('Current') ||
                    text.match(/\b[A-Z][a-z]+\b/) // Capitalized words (likely parameters)
                )) {
                    options.push(text);
                }
            });

            return [...new Set(options)]; // Remove duplicates
        });

        if (parameterOptions.length > 0) {
            console.log(`Found ${parameterOptions.length} parameters:`);
            parameterOptions.forEach(param => {
                console.log(`  - ${param}`);
            });

            this.findings.parameterGraphs[`${siteName}_${tabName}`] = {
                parameters: parameterOptions,
                timestamp: new Date().toISOString()
            };
        }
    }

    async generateCompleteWorkflowReport() {
        console.log('📊 Generating complete workflow report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            exploration: {
                totalSites: Object.keys(this.findings.individualSites).length,
                totalTabs: Object.keys(this.findings.siteTabsData).length,
                totalScreenshots: this.findings.screenshots.length,
                totalAPIs: this.findings.apis.length
            },
            sitesTable: this.findings.sitesTable,
            siteTabsData: this.findings.siteTabsData,
            parameterGraphs: this.findings.parameterGraphs,
            screenshots: this.findings.screenshots,
            apiCalls: this.findings.apis,
            summary: {
                workflowSteps: [
                    "1. Login to Hydroview platform",
                    "2. Navigate to Sites section in sidebar",
                    "3. View sites table grid",
                    "4. Click on individual site",
                    "5. Explore multiple tabs per site",
                    "6. Analyze parameter graphs in Data Explorer"
                ],
                tabsFound: Object.keys(this.findings.siteTabsData),
                screenshotsCaptured: this.findings.screenshots.map(s => s.filename)
            }
        };

        fs.writeFileSync('complete-site-workflow-report.json', JSON.stringify(report, null, 2));
        console.log('✅ Complete workflow report saved');
        return report;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new CompleteSiteWorkflowExplorer();
    
    try {
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        await explorer.init();
        await explorer.login();
        
        // Step 1: Navigate to Sites section
        const sitesNavigated = await explorer.navigateToSitesSection();
        if (!sitesNavigated) {
            console.log('❌ Could not navigate to Sites section');
            return;
        }
        
        // Step 2: Explore sites table grid
        const sites = await explorer.exploreSitesTable();
        
        // Step 3: Explore individual sites and their tabs
        for (let i = 0; i < Math.min(sites.length, 3); i++) { // Limit to first 3 sites
            const site = sites[i];
            if (site.text && site.text.length > 2) {
                console.log(`\n--- Exploring Site ${i + 1}/${Math.min(sites.length, 3)} ---`);
                await explorer.exploreIndividualSite(site.text, i);
                await explorer.page.waitForTimeout(2000);
            }
        }
        
        const report = await explorer.generateCompleteWorkflowReport();
        
        console.log('\n🎉 Complete site workflow exploration finished!');
        console.log('📊 Summary:');
        console.log(`  - Sites in table: ${sites.length}`);
        console.log(`  - Tabs explored: ${report.exploration.totalTabs}`);
        console.log(`  - Screenshots: ${report.exploration.totalScreenshots}`);
        console.log(`  - API calls: ${report.exploration.totalAPIs}`);
        
    } catch (error) {
        console.error('❌ Workflow exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = CompleteSiteWorkflowExplorer;
