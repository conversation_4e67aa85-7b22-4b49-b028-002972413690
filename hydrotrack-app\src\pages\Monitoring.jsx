import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Button,
  Icon
} from "../components/Component";
import { Card, CardBody, Badge, Progress } from "reactstrap";
import { Line, Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend
);

const Monitoring = () => {
  const [realTimeData, setRealTimeData] = useState({
    waterLevels: [],
    discharge: [],
    weather: [],
    lastUpdate: new Date()
  });

  const [selectedSite, setSelectedSite] = useState("all");

  useEffect(() => {
    // Simulate real-time data updates
    const interval = setInterval(() => {
      updateRealTimeData();
    }, 5000); // Update every 5 seconds

    // Initial data load
    updateRealTimeData();

    return () => clearInterval(interval);
  }, []);

  const updateRealTimeData = () => {
    const now = new Date();
    const timeLabels = Array.from({length: 12}, (_, i) => {
      const time = new Date(now.getTime() - (11-i) * 5 * 60000);
      return time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
    });

    setRealTimeData({
      waterLevels: {
        labels: timeLabels,
        datasets: [
          {
            label: 'Ganges - Haridwar (m)',
            data: Array.from({length: 12}, () => 12.5 + Math.random() * 2),
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
          },
          {
            label: 'Yamuna - Delhi (m)',
            data: Array.from({length: 12}, () => 8.2 + Math.random() * 1.5),
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            tension: 0.1
          }
        ]
      },
      discharge: {
        labels: timeLabels,
        datasets: [
          {
            label: 'Discharge Rate (m³/s)',
            data: Array.from({length: 12}, () => 150 + Math.random() * 50),
            backgroundColor: 'rgba(54, 162, 235, 0.8)',
            borderColor: 'rgba(54, 162, 235, 1)',
            borderWidth: 1
          }
        ]
      },
      weather: {
        temperature: 28 + Math.random() * 5,
        humidity: 65 + Math.random() * 20,
        rainfall: Math.random() * 10,
        windSpeed: 5 + Math.random() * 10
      },
      lastUpdate: now
    });
  };

  const sites = [
    { id: "all", name: "All Sites" },
    { id: "ganges", name: "Ganges - Haridwar" },
    { id: "yamuna", name: "Yamuna - Delhi" },
    { id: "mumbai", name: "Weather Station - Mumbai" }
  ];

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top',
      },
      title: {
        display: true,
        text: 'Real-time Water Monitoring Data'
      }
    },
    scales: {
      y: {
        beginAtZero: false
      }
    }
  };

  return (
    <React.Fragment>
      <Head title="Real-time Monitoring - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Real-time Monitoring</BlockTitle>
              <p className="text-soft">
                Live data from water monitoring stations
                <span className="text-primary ms-2">
                  Last updated: {realTimeData.lastUpdate.toLocaleTimeString()}
                </span>
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button color="primary" outline>
                  <Icon name="reload"></Icon>
                  <span>Refresh Data</span>
                </Button>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        {/* Site Filter */}
        <Block>
          <Row className="g-gs">
            <Col md="12">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Site Filter</h6>
                    </div>
                  </div>
                  <div className="btn-group" role="group">
                    {sites.map((site) => (
                      <Button
                        key={site.id}
                        color={selectedSite === site.id ? "primary" : "light"}
                        onClick={() => setSelectedSite(site.id)}
                        size="sm"
                      >
                        {site.name}
                      </Button>
                    ))}
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>

        {/* Real-time Charts */}
        <Block>
          <Row className="g-gs">
            <Col lg="8">
              <Card className="card-bordered h-100">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Water Levels - Real-time</h6>
                    </div>
                    <div className="card-tools">
                      <Badge color="success" className="badge-dim">
                        <Icon name="activity"></Icon> Live
                      </Badge>
                    </div>
                  </div>
                  <div style={{ height: '300px' }}>
                    {realTimeData.waterLevels.labels && (
                      <Line 
                        data={realTimeData.waterLevels} 
                        options={{
                          ...chartOptions,
                          maintainAspectRatio: false,
                          plugins: {
                            ...chartOptions.plugins,
                            title: {
                              display: true,
                              text: 'Water Levels (meters)'
                            }
                          }
                        }} 
                      />
                    )}
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col lg="4">
              <Card className="card-bordered h-100">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Weather Conditions</h6>
                    </div>
                  </div>
                  
                  <div className="nk-tb-list">
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-warning">
                            <Icon name="sun"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Temperature</span>
                            <span className="tb-sub">{realTimeData.weather.temperature?.toFixed(1)}°C</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-info">
                            <Icon name="droplet"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Humidity</span>
                            <span className="tb-sub">{realTimeData.weather.humidity?.toFixed(0)}%</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-primary">
                            <Icon name="cloud-rain"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Rainfall</span>
                            <span className="tb-sub">{realTimeData.weather.rainfall?.toFixed(1)} mm</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="nk-tb-item">
                      <div className="nk-tb-col">
                        <div className="user-card">
                          <div className="user-avatar bg-success">
                            <Icon name="wind"></Icon>
                          </div>
                          <div className="user-info">
                            <span className="tb-lead">Wind Speed</span>
                            <span className="tb-sub">{realTimeData.weather.windSpeed?.toFixed(1)} km/h</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>

        {/* Discharge Data */}
        <Block>
          <Row className="g-gs">
            <Col lg="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">Discharge Rates</h6>
                    </div>
                  </div>
                  <div style={{ height: '250px' }}>
                    {realTimeData.discharge.labels && (
                      <Bar 
                        data={realTimeData.discharge} 
                        options={{
                          ...chartOptions,
                          maintainAspectRatio: false,
                          plugins: {
                            ...chartOptions.plugins,
                            title: {
                              display: true,
                              text: 'Water Discharge (m³/s)'
                            }
                          }
                        }} 
                      />
                    )}
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col lg="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group">
                    <div className="card-title">
                      <h6 className="title">System Health</h6>
                    </div>
                  </div>
                  
                  <div className="row g-3">
                    <div className="col-6">
                      <div className="form-group">
                        <label className="form-label">Data Transmission</label>
                        <Progress value={95} color="success" />
                        <span className="form-note">95% Success Rate</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="form-group">
                        <label className="form-label">Equipment Status</label>
                        <Progress value={88} color="warning" />
                        <span className="form-note">88% Operational</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="form-group">
                        <label className="form-label">Network Connectivity</label>
                        <Progress value={92} color="info" />
                        <span className="form-note">92% Uptime</span>
                      </div>
                    </div>
                    <div className="col-6">
                      <div className="form-group">
                        <label className="form-label">Data Quality</label>
                        <Progress value={97} color="success" />
                        <span className="form-note">97% Valid Data</span>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>
      </Content>
    </React.Fragment>
  );
};

export default Monitoring;
