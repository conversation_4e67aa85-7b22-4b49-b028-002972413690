const { chromium } = require('playwright');
const fs = require('fs');

class FocusedSiteTabsExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            waterLevelSite: {},
            dischargeSite: {},
            tabsData: {},
            screenshots: [],
            apis: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
        this.screenshotCounter = 1;
    }

    async init() {
        console.log('🚀 Initializing focused site tabs exploration...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1500
        });
        this.page = await this.browser.newPage();
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Monitor API calls
        this.page.on('request', request => {
            if (request.url().includes('api')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    timestamp: new Date().toISOString()
                });
            }
        });
    }

    async takeScreenshot(name, description) {
        const filename = `screenshots/focused-${String(this.screenshotCounter).padStart(2, '0')}-${name}.png`;
        await this.page.screenshot({ path: filename, fullPage: true });
        this.findings.screenshots.push({
            filename: filename,
            description: description,
            timestamp: new Date().toISOString()
        });
        this.screenshotCounter++;
        console.log(`📸 ${filename} - ${description}`);
    }

    async login() {
        console.log('🔐 Logging in...');
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
        await this.page.waitForTimeout(3000);

        await this.page.fill('input[type="text"]', this.credentials.username);
        await this.page.fill('input[type="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        
        await this.page.waitForTimeout(8000);
        await this.takeScreenshot('login-complete', 'Dashboard after login');
        
        console.log('✅ Login successful');
        return true;
    }

    async navigateToSites() {
        console.log('🏢 Navigating to Sites section...');

        // Step 1: Click hamburger icon to open sidebar
        console.log('  📱 Step 1: Opening hamburger menu...');
        try {
            // Look for hamburger menu icon
            const hamburgerSelectors = [
                'button[aria-label="menu"]',
                'button:has-text("menu")',
                '.q-btn:has-text("menu")',
                'i:has-text("menu")',
                '.q-icon:has-text("menu")',
                'button .q-icon:has-text("menu")'
            ];

            let hamburgerClicked = false;
            for (const selector of hamburgerSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    for (const element of elements) {
                        const isVisible = await element.isVisible();
                        if (isVisible) {
                            await element.click();
                            await this.page.waitForTimeout(2000);
                            hamburgerClicked = true;
                            console.log(`    ✅ Hamburger menu clicked using: ${selector}`);
                            break;
                        }
                    }
                    if (hamburgerClicked) break;
                } catch (e) {
                    continue;
                }
            }

            if (!hamburgerClicked) {
                console.log('    ❌ Could not find hamburger menu');
                return false;
            }

            await this.takeScreenshot('sidebar-opened', 'Sidebar opened after hamburger click');

            // Step 2: Click on Sites menu in sidebar
            console.log('  🏢 Step 2: Clicking Sites menu in sidebar...');

            const sitesMenuSelectors = [
                'text="Sites"',
                'text="listSites"',
                '.q-item:has-text("Sites")',
                '.q-item:has-text("list")',
                '.q-item[href*="sites"]'
            ];

            let sitesClicked = false;
            for (const selector of sitesMenuSelectors) {
                try {
                    await this.page.click(selector);
                    await this.page.waitForTimeout(5000);
                    sitesClicked = true;
                    console.log(`    ✅ Sites menu clicked using: ${selector}`);
                    break;
                } catch (e) {
                    console.log(`    Failed with selector: ${selector}`);
                    continue;
                }
            }

            // Alternative: Try clicking the second q-item (Sites is usually second)
            if (!sitesClicked) {
                try {
                    const siteItems = await this.page.$$('.q-item');
                    if (siteItems[1]) {
                        await siteItems[1].click();
                        await this.page.waitForTimeout(5000);
                        sitesClicked = true;
                        console.log('    ✅ Sites menu clicked using second q-item');
                    }
                } catch (e) {
                    console.log('    Failed with second q-item approach');
                }
            }

            if (sitesClicked) {
                await this.takeScreenshot('sites-table', 'Sites table loaded');
                console.log('  ✅ Successfully navigated to Sites section');
                return true;
            } else {
                console.log('  ❌ Could not click Sites menu');
                return false;
            }

        } catch (error) {
            console.error('Failed to navigate to Sites:', error.message);
            return false;
        }
    }

    async findAndClickSite(siteType) {
        console.log(`\n🔍 Step 3: Looking for ${siteType} site in the sites table...`);

        // Define search patterns for different site types
        const searchPatterns = {
            'Water Level': ['DTPL Test Water Level', 'DTPL TEST Water Level', 'DTPLTEST Water Level', 'Water Level'],
            'Downstream Discharge': ['UJVNL-Gamri Gad Downstream Discharge', 'Downstream Discharge', 'UJVNL', 'Gamri Gad']
        };

        const patterns = searchPatterns[siteType];

        // First, let's see what sites are available in the table
        console.log('  📋 Scanning sites table for available sites...');
        const availableSites = await this.page.evaluate(() => {
            const sites = [];
            // Look in table cells for site names
            document.querySelectorAll('td, .q-td').forEach(cell => {
                const text = cell.textContent?.trim();
                if (text && text.length > 5 && text.length < 100) {
                    sites.push(text);
                }
            });
            return sites;
        });

        console.log('  Available sites found:');
        availableSites.forEach(site => {
            console.log(`    - ${site}`);
        });

        for (const pattern of patterns) {
            try {
                console.log(`  🎯 Trying to click: ${pattern}`);

                // Strategy 1: Direct text click
                try {
                    await this.page.click(`text="${pattern}"`);
                    await this.page.waitForTimeout(5000);

                    const hasTabs = await this.page.$('.q-tab, .tab, [role="tab"]');
                    if (hasTabs) {
                        console.log(`  ✅ Successfully clicked on ${pattern} using direct text`);
                        await this.takeScreenshot(`${siteType.replace(/\s+/g, '-').toLowerCase()}-site`, `${siteType} site loaded`);
                        return pattern;
                    }
                } catch (e) {
                    console.log(`    Direct text click failed: ${e.message}`);
                }

                // Strategy 2: Look for pattern in table cells and click
                try {
                    const cells = await this.page.$$('td, .q-td');
                    for (const cell of cells) {
                        const text = await cell.textContent();
                        if (text && text.includes(pattern)) {
                            console.log(`    Found "${pattern}" in cell: ${text}`);
                            await cell.click();
                            await this.page.waitForTimeout(5000);

                            const hasTabs = await this.page.$('.q-tab, .tab, [role="tab"]');
                            if (hasTabs) {
                                console.log(`  ✅ Successfully clicked on ${pattern} using table cell`);
                                await this.takeScreenshot(`${siteType.replace(/\s+/g, '-').toLowerCase()}-site`, `${siteType} site loaded`);
                                return pattern;
                            }
                        }
                    }
                } catch (e) {
                    console.log(`    Table cell click failed: ${e.message}`);
                }

                // Strategy 3: Look for clickable links containing the pattern
                try {
                    const links = await this.page.$$('a, .clickable, .q-btn');
                    for (const link of links) {
                        const text = await link.textContent();
                        if (text && text.includes(pattern)) {
                            console.log(`    Found "${pattern}" in link: ${text}`);
                            await link.click();
                            await this.page.waitForTimeout(5000);

                            const hasTabs = await this.page.$('.q-tab, .tab, [role="tab"]');
                            if (hasTabs) {
                                console.log(`  ✅ Successfully clicked on ${pattern} using link`);
                                await this.takeScreenshot(`${siteType.replace(/\s+/g, '-').toLowerCase()}-site`, `${siteType} site loaded`);
                                return pattern;
                            }
                        }
                    }
                } catch (e) {
                    console.log(`    Link click failed: ${e.message}`);
                }

                // Strategy 4: Partial text match
                try {
                    const partialPattern = pattern.split(' ')[0]; // First word
                    await this.page.click(`text*="${partialPattern}"`);
                    await this.page.waitForTimeout(5000);

                    const hasTabs = await this.page.$('.q-tab, .tab, [role="tab"]');
                    if (hasTabs) {
                        console.log(`  ✅ Successfully clicked on ${pattern} using partial match`);
                        await this.takeScreenshot(`${siteType.replace(/\s+/g, '-').toLowerCase()}-site`, `${siteType} site loaded`);
                        return pattern;
                    }
                } catch (e) {
                    console.log(`    Partial text click failed: ${e.message}`);
                }

            } catch (error) {
                console.log(`  Failed to click ${pattern}:`, error.message);
            }
        }

        console.log(`❌ Could not find and click ${siteType} site`);
        console.log('Available sites were:', availableSites.slice(0, 10)); // Show first 10
        return null;
    }

    async exploreSiteTabs(siteType, siteName) {
        console.log(`\n📋 Exploring tabs for ${siteType} site: ${siteName}`);
        
        const tabsToExplore = [
            "Latest Data",
            "Data Explorer", 
            "Site Info",
            "Equipment Data",
            "Notes",
            "Documents"
        ];

        const siteData = {
            siteName: siteName,
            siteType: siteType,
            tabs: {},
            parameters: [],
            dataTypes: []
        };

        for (let i = 0; i < tabsToExplore.length; i++) {
            const tabName = tabsToExplore[i];
            console.log(`  📊 Exploring tab: ${tabName}`);
            
            try {
                // Try to click on the tab
                const tabClicked = await this.clickTab(tabName);
                
                if (tabClicked) {
                    await this.page.waitForTimeout(4000);
                    
                    // Take screenshot
                    const safeTabName = tabName.replace(/\s+/g, '-').toLowerCase();
                    const safeSiteName = siteType.replace(/\s+/g, '-').toLowerCase();
                    await this.takeScreenshot(`${safeSiteName}-${safeTabName}`, `${siteType} - ${tabName} tab`);
                    
                    // Analyze tab content
                    const tabData = await this.analyzeTabContent(tabName);
                    siteData.tabs[tabName] = tabData;
                    
                    // Extract parameters
                    if (tabData.parameters) {
                        siteData.parameters = [...new Set([...siteData.parameters, ...tabData.parameters])];
                    }
                    
                    console.log(`    ✅ Tab analyzed: ${Object.keys(tabData).length} data elements`);
                } else {
                    console.log(`    ❌ Could not click on tab: ${tabName}`);
                }
            } catch (error) {
                console.error(`    ❌ Error exploring tab ${tabName}:`, error.message);
            }
        }

        return siteData;
    }

    async clickTab(tabName) {
        const strategies = [
            async () => await this.page.click(`text="${tabName}"`),
            async () => await this.page.click(`.q-tab:has-text("${tabName}")`),
            async () => await this.page.click(`button:has-text("${tabName}")`),
            async () => await this.page.click(`[role="tab"]:has-text("${tabName}")`),
            async () => {
                // Try partial text match
                const firstWord = tabName.split(' ')[0];
                await this.page.click(`text*="${firstWord}"`);
            },
            async () => {
                // Look for any element containing the tab name
                const elements = await this.page.$$('*');
                for (const element of elements) {
                    try {
                        const text = await element.textContent();
                        if (text && text.trim() === tabName) {
                            const isVisible = await element.isVisible();
                            if (isVisible) {
                                await element.click();
                                return;
                            }
                        }
                    } catch (e) {
                        continue;
                    }
                }
            }
        ];

        for (let i = 0; i < strategies.length; i++) {
            try {
                await strategies[i]();
                return true;
            } catch (e) {
                continue;
            }
        }
        return false;
    }

    async analyzeTabContent(tabName) {
        console.log(`      🔍 Analyzing ${tabName} content...`);
        
        const content = await this.page.evaluate(() => {
            const analysis = {
                parameters: [],
                values: [],
                tables: [],
                charts: [],
                buttons: [],
                forms: [],
                headings: [],
                timestamps: []
            };

            // Get all text content and analyze for parameters
            const allText = document.body.textContent || '';
            const lines = allText.split('\n').map(line => line.trim()).filter(line => line.length > 0);
            
            lines.forEach(line => {
                // Look for parameter-like text
                if (line.match(/^[A-Za-z\s]+:/) || line.match(/[A-Za-z\s]+\s*\d+/)) {
                    analysis.parameters.push(line);
                }
                // Look for numeric values
                if (line.match(/^\d+\.?\d*\s*[A-Za-z]*$/)) {
                    analysis.values.push(line);
                }
                // Look for timestamps
                if (line.match(/\d{4}-\d{2}-\d{2}|\d{2}:\d{2}:\d{2}/)) {
                    analysis.timestamps.push(line);
                }
            });

            // Analyze tables
            document.querySelectorAll('table, .q-table').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent?.trim());
                const rows = Array.from(table.querySelectorAll('tr')).slice(1).map(row => {
                    return Array.from(row.querySelectorAll('td')).map(td => td.textContent?.trim());
                });

                analysis.tables.push({
                    id: `table-${index}`,
                    headers: headers,
                    rows: rows.slice(0, 5), // First 5 rows only
                    totalRows: rows.length
                });
            });

            // Analyze charts
            document.querySelectorAll('canvas, svg, .chart, .graph').forEach((chart, index) => {
                analysis.charts.push({
                    type: chart.tagName,
                    id: chart.id || `chart-${index}`,
                    classes: chart.className,
                    width: chart.width || chart.clientWidth,
                    height: chart.height || chart.clientHeight
                });
            });

            // Get headings
            document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(h => {
                analysis.headings.push(h.textContent?.trim());
            });

            // Get buttons
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent?.trim();
                if (text && text.length < 100) {
                    analysis.buttons.push(text);
                }
            });

            // Get forms
            document.querySelectorAll('form, .q-form').forEach((form, index) => {
                const inputs = Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
                    type: input.type,
                    name: input.name,
                    placeholder: input.placeholder
                }));
                analysis.forms.push({
                    id: `form-${index}`,
                    inputs: inputs
                });
            });

            return analysis;
        });

        return content;
    }

    async generateFocusedReport() {
        console.log('📊 Generating focused site tabs report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            exploration: {
                waterLevelSite: this.findings.waterLevelSite,
                dischargeSite: this.findings.dischargeSite,
                totalScreenshots: this.findings.screenshots.length,
                totalAPIs: this.findings.apis.length
            },
            tabsData: this.findings.tabsData,
            screenshots: this.findings.screenshots,
            apiCalls: this.findings.apis,
            summary: {
                sitesExplored: [
                    this.findings.waterLevelSite.siteName,
                    this.findings.dischargeSite.siteName
                ].filter(Boolean),
                parametersFound: [
                    ...(this.findings.waterLevelSite.parameters || []),
                    ...(this.findings.dischargeSite.parameters || [])
                ],
                screenshotsCaptured: this.findings.screenshots.map(s => s.filename)
            }
        };

        fs.writeFileSync('focused-site-tabs-report.json', JSON.stringify(report, null, 2));
        console.log('✅ Focused site tabs report saved');
        return report;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new FocusedSiteTabsExplorer();
    
    try {
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        await explorer.init();
        await explorer.login();
        
        // Navigate to Sites section
        const sitesNavigated = await explorer.navigateToSites();
        if (!sitesNavigated) {
            console.log('❌ Could not navigate to Sites section');
            return;
        }
        
        // Explore Water Level site
        console.log('\n=== EXPLORING WATER LEVEL SITE ===');
        const waterLevelSite = await explorer.findAndClickSite('Water Level');
        if (waterLevelSite) {
            const waterLevelData = await explorer.exploreSiteTabs('Water Level', waterLevelSite);
            explorer.findings.waterLevelSite = waterLevelData;
            explorer.findings.tabsData['Water Level'] = waterLevelData;
        }
        
        // Navigate back to sites list
        await explorer.navigateToSites();
        await explorer.page.waitForTimeout(3000);
        
        // Explore Downstream Discharge site
        console.log('\n=== EXPLORING DOWNSTREAM DISCHARGE SITE ===');
        const dischargeSite = await explorer.findAndClickSite('Downstream Discharge');
        if (dischargeSite) {
            const dischargeData = await explorer.exploreSiteTabs('Downstream Discharge', dischargeSite);
            explorer.findings.dischargeSite = dischargeData;
            explorer.findings.tabsData['Downstream Discharge'] = dischargeData;
        }
        
        const report = await explorer.generateFocusedReport();
        
        console.log('\n🎉 Focused site tabs exploration completed!');
        console.log('📊 Summary:');
        console.log(`  - Water Level site: ${explorer.findings.waterLevelSite.siteName || 'Not found'}`);
        console.log(`  - Discharge site: ${explorer.findings.dischargeSite.siteName || 'Not found'}`);
        console.log(`  - Screenshots: ${report.exploration.totalScreenshots}`);
        console.log(`  - API calls: ${report.exploration.totalAPIs}`);
        
        if (explorer.findings.waterLevelSite.parameters) {
            console.log(`  - Water Level parameters: ${explorer.findings.waterLevelSite.parameters.length}`);
        }
        if (explorer.findings.dischargeSite.parameters) {
            console.log(`  - Discharge parameters: ${explorer.findings.dischargeSite.parameters.length}`);
        }
        
    } catch (error) {
        console.error('❌ Focused exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = FocusedSiteTabsExplorer;
