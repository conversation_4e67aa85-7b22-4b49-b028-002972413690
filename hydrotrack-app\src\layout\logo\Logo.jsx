import React from "react";
import LogoLight2x from "@/images/logo2x.png";
import LogoDark2x from "@/images/logo-dark2x.png";
import {Link} from "react-router-dom";

const Logo = () => {
  return (
    <Link to={`/`} className="logo-link">
      <div className="logo-text" style={{
        color: '#364a63',
        fontSize: '1.5rem',
        fontWeight: 'bold',
        display: 'flex',
        alignItems: 'center',
        gap: '8px'
      }}>
        <span style={{ color: '#1976d2' }}>💧</span>
        HydroTrack
      </div>
    </Link>
  );
};

export default Logo;
