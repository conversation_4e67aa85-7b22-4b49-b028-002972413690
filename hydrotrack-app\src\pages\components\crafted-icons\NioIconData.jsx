export const iconData = [
{"icon-name":"device-new"},
{"icon-name":"drag"},
{"icon-name":"expired-clock"},
{"icon-name":"language-new"},
{"icon-name":"link-disabled"},
{"icon-name":"mouse"},
{"icon-name":"operating-system"},
{"icon-name":"random-new"},
{"icon-name":"discord"},
{"icon-name":"discord-fill"},
{"icon-name":"discord-msg"},
{"icon-name":"line"},
{"icon-name":"line-fill"},
{"icon-name":"line-alt"},
{"icon-name":"line-alt-fill"},
{"icon-name":"tiktok"},
{"icon-name":"tiktok-fill"},
{"icon-name":"tiktok-alt"},
{"icon-name":"tiktok-alt-fill"},
{"icon-name":"x"},
{"icon-name":"x-fill"},
{"icon-name":"x-round-fill"},
{"icon-name":"instagram-fill"},
{"icon-name":"threads"},
{"icon-name":"threads-fill"},
{"icon-name":"twitch"},
{"icon-name":"twitch-fill"},
{"icon-name":"tailwind"},
{"icon-name":"wechat"},
{"icon-name":"wechat-fill"},
{"icon-name":"wechat-alt-fill"},
{"icon-name":"graphql"},
{"icon-name":"node-js"},
{"icon-name":"nioboard"},
{"icon-name":"svelte"},
{"icon-name":"typescript"},
{"icon-name":"vue"},
{"icon-name":"linux"},
{"icon-name":"b-chrome"},
{"icon-name":"b-firefox"},
{"icon-name":"b-edge"},
{"icon-name":"b-ie"},
{"icon-name":"b-opera"},
{"icon-name":"b-safari"},
{"icon-name":"b-uc"},
{"icon-name":"google-play-store-alt"},
{"icon-name":"node"},
{"icon-name":"amazon-full"},
{"icon-name":"discord-full"},
{"icon-name":"dribbble-full"},
{"icon-name":"facebook-full"},
{"icon-name":"google-full"},
{"icon-name":"googlemaps-full"},
{"icon-name":"instagram-full"},
{"icon-name":"linkedin-full"},
{"icon-name":"microsoft-full"},
{"icon-name":"pinterest-full"},
{"icon-name":"skype-full"},
{"icon-name":"spotify-full"},
{"icon-name":"vimeo-full"},
{"icon-name":"vine-full"},
{"icon-name":"whatsapp-full"},
{"icon-name":"windows-full"},
{"icon-name":"youtube-full"},
{"icon-name":"curve-down-right-fill"},
{"icon-name":"curve-right-down-fill"},
{"icon-name":"curve-right-up-fill"},
{"icon-name":"curve-up-right-fill"},
{"icon-name":"loader-fill"},
{"icon-name":"pwa-fill"},
{"icon-name":"pwa"},
{"icon-name":"question-fill"},
{"icon-name":"text2-fill"},
{"icon-name":"wifi-fill"},
{"icon-name":"wifi-off-fill"},
{"icon-name":"app-development"},
{"icon-name":"design-tool"},
{"icon-name":"web-development"},
{"icon-name":"img-alt"},
{"icon-name":"brain-fill"},
{"icon-name":"brain"},
{"icon-name":"briefcase-fill"},
{"icon-name":"button-fill"},
{"icon-name":"button"},
{"icon-name":"carousel-fill"},
{"icon-name":"carousel"},
{"icon-name":"cart-alt-fill"},
{"icon-name":"cart-alt"},
{"icon-name":"checkbox-checked"},
{"icon-name":"checkbox"},
{"icon-name":"cookie-fill"},
{"icon-name":"cookie"},
{"icon-name":"copy-page-fill"},
{"icon-name":"copy-page"},
{"icon-name":"create-icon"},
{"icon-name":"create-icon-fill"},
{"icon-name":"teeth-fill"},
{"icon-name":"teeth"},
{"icon-name":"edit-profile-fill"},
{"icon-name":"edit-profile"},
{"icon-name":"fastfood-fill"},
{"icon-name":"fastfood"},
{"icon-name":"form-validation-fill"},
{"icon-name":"form-validation"},
{"icon-name":"home-new"},
{"icon-name":"home-new-fill"},
{"icon-name":"input-fill"},
{"icon-name":"input"},
{"icon-name":"lung-fill"},
{"icon-name":"lung"},
{"icon-name":"plus-alt-fill"},
{"icon-name":"plus-alt"},
{"icon-name":"progress-bar-fill"},
{"icon-name":"progress-bar"},
{"icon-name":"radio-checked"},
{"icon-name":"radio"},
{"icon-name":"request-fill"},
{"icon-name":"request"},
{"icon-name":"lock-question-fiil"},
{"icon-name":"lock-question"},
{"icon-name":"stepper-fill"},
{"icon-name":"stepper"},
{"icon-name":"system-status-fill"},
{"icon-name":"system-status"},
{"icon-name":"terms-conditions-fill"},
{"icon-name":"terms-conditions"},
{"icon-name":"masks-fill"},
{"icon-name":"masks"},
{"icon-name":"ticket-off-fill"},
{"icon-name":"ticket-off"},
{"icon-name":"timeline-fill"},
{"icon-name":"timeline"},
{"icon-name":"toasts-fill"},
{"icon-name":"toasts"},
{"icon-name":"toggle-fill"},
{"icon-name":"toggle"},
{"icon-name":"view-x2-alt"},
{"icon-name":"view-x4-alt"},
{"icon-name":"view-x6-alt"},
{"icon-name":"wallet-new-fill"},
{"icon-name":"wallet-new"},
{"icon-name":"bugs-alt-fill"},
{"icon-name":"bugs-alt"},
{"icon-name":"bulb-alt-fill"},
{"icon-name":"bulb-alt"},
{"icon-name":"chat-msg-fill"},
{"icon-name":"chat-msg"},
{"icon-name":"diamond-fill"},
{"icon-name":"diamond"},
{"icon-name":"file-code-fill"},
{"icon-name":"laptop-fill"},
{"icon-name":"mail-alt"},
{"icon-name":"newspaper-fill"},
{"icon-name":"newspaper"},
{"icon-name":"pie-2-fill"},
{"icon-name":"pie-2"},
{"icon-name":"presentation-fill"},
{"icon-name":"presentation"},
{"icon-name":"projector-screen-fill"},
{"icon-name":"projector-screen"},
{"icon-name":"quote-sq-left-fill"},
{"icon-name":"quote-sq-left"},
{"icon-name":"quote-sq-right-fill"},
{"icon-name":"quote-sq-right"},
{"icon-name":"send-fill"},
{"icon-name":"setting-question-fill"},
{"icon-name":"setting-question"},
{"icon-name":"support-fill"},
{"icon-name":"support"},
{"icon-name":"traffic-signal-fill"},
{"icon-name":"traffic-signal"},
{"icon-name":"tree-structure-fill"},
{"icon-name":"tree-structure"},
{"icon-name":"trophy-fill"},
{"icon-name":"trophy"},
{"icon-name":"star-award"},
{"icon-name":"star-award-fill"},
{"icon-name":"award-fill"},
{"icon-name":"medal"},
{"icon-name":"medal-fill"},
{"icon-name":"user-group-fill"},
{"icon-name":"user-group"},
{"icon-name":"user-switch-fill"},
{"icon-name":"user-switch"},
{"icon-name":"centos"},
{"icon-name":"covid"},
{"icon-name":"fedora"},
{"icon-name":"hot-fill"},
{"icon-name":"hot"},
{"icon-name":"linux-server"},
{"icon-name":"note-add-fill"},
{"icon-name":"repeat-fill"},
{"icon-name":"tranx-fill"},
{"icon-name":"ubuntu"},
{"icon-name":"virus"},
{"icon-name":"b-si"},
{"icon-name":"brick-fill"},
{"icon-name":"brick"},
{"icon-name":"col-3s"},
{"icon-name":"col-4s"},
{"icon-name":"col-2s"},
{"icon-name":"comments"},
{"icon-name":"dot-sq"},
{"icon-name":"dot"},
{"icon-name":"footer"},
{"icon-name":"header"},
{"icon-name":"heading"},
{"icon-name":"layout-alt-fill"},
{"icon-name":"layout-alt"},
{"icon-name":"layout-fill1"},
{"icon-name":"layout1"},
{"icon-name":"list-index-fill"},
{"icon-name":"list-index"},
{"icon-name":"list-thumb-alt-fill"},
{"icon-name":"list-thumb-alt"},
{"icon-name":"list-thumb-fill"},
{"icon-name":"list-thumb"},
{"icon-name":"masonry-fill"},
{"icon-name":"masonry"},
{"icon-name":"menu-circled"},
{"icon-name":"menu-squared"},
{"icon-name":"notice"},
{"icon-name":"pen2"},
{"icon-name":"propert-blank"},
{"icon-name":"property-add"},
{"icon-name":"property-alt"},
{"icon-name":"property-remove"},
{"icon-name":"property"},
{"icon-name":"puzzle-fill"},
{"icon-name":"puzzle"},
{"icon-name":"quote-left"},
{"icon-name":"quote-right"},
{"icon-name":"row-mix"},
{"icon-name":"row-view1"},
{"icon-name":"sidebar-r"},
{"icon-name":"text2"},
{"icon-name":"tile-thumb-fill"},
{"icon-name":"tile-thumb"},
{"icon-name":"view-col-fill"},
{"icon-name":"view-col-sq"},
{"icon-name":"view-col"},
{"icon-name":"view-col2"},
{"icon-name":"view-col3"},
{"icon-name":"view-cols-fill"},
{"icon-name":"view-cols-sq"},
{"icon-name":"view-cols"},
{"icon-name":"view-grid-fill"},
{"icon-name":"view-grid-sq"},
{"icon-name":"view-grid-wd"},
{"icon-name":"view-grid"},
{"icon-name":"view-grid2-wd"},
{"icon-name":"view-grid3-wd"},
{"icon-name":"view-group-fill"},
{"icon-name":"view-group-wd"},
{"icon-name":"view-list-fill"},
{"icon-name":"view-list-sq"},
{"icon-name":"view-list-wd"},
{"icon-name":"view-list"},
{"icon-name":"view-panel-fill"},
{"icon-name":"view-panel-sq"},
{"icon-name":"view-panel"},
{"icon-name":"view-row-fill"},
{"icon-name":"view-row-sq"},
{"icon-name":"view-row-wd"},
{"icon-name":"view-row"},
{"icon-name":"view-x1"},
{"icon-name":"view-x2"},
{"icon-name":"view-x3"},
{"icon-name":"view-x4"},
{"icon-name":"view-x5"},
{"icon-name":"view-x6"},
{"icon-name":"view-x7"},
{"icon-name":"dashlite"},
{"icon-name":"dashlite-circle"},
{"icon-name":"dashlite-alt"},
{"icon-name":"master-card"},
{"icon-name":"paypal"},
{"icon-name":"visa-alt"},
{"icon-name":"coin-eur"},
{"icon-name":"coin-gbp"},
{"icon-name":"sign-ada-alt"},
{"icon-name":"sign-bch-alt"},
{"icon-name":"sign-bgp-alt"},
{"icon-name":"sign-bnb-alt"},
{"icon-name":"sign-brl-alt"},
{"icon-name":"sign-btc-alt"},
{"icon-name":"sign-cc-alt"},
{"icon-name":"sign-cc-alt2"},
{"icon-name":"sign-chf-alt"},
{"icon-name":"sign-cny-alt"},
{"icon-name":"sign-czk-alt"},
{"icon-name":"sign-dash-alt"},
{"icon-name":"sign-dkk-alt"},
{"icon-name":"sign-eos-alt"},
{"icon-name":"sign-eth-alt"},
{"icon-name":"sign-eur-alt2"},
{"icon-name":"sign-euro-alt"},
{"icon-name":"sign-gbp-alt2"},
{"icon-name":"sign-hkd-alt"},
{"icon-name":"sign-idr-alt"},
{"icon-name":"sign-inr-alt"},
{"icon-name":"sign-jpy-alt"},
{"icon-name":"sign-kr-alt"},
{"icon-name":"sign-ltc-alt"},
{"icon-name":"sign-ltc"},
{"icon-name":"sign-mxn-alt"},
{"icon-name":"sign-mxr-alt"},
{"icon-name":"sign-myr-alt"},
{"icon-name":"sign-paypal-alt"},
{"icon-name":"sign-paypal-full"},
{"icon-name":"sign-php-alt"},
{"icon-name":"sign-pln-alt"},
{"icon-name":"sign-rub-alt"},
{"icon-name":"sign-sek-alt"},
{"icon-name":"sign-sgd-alt"},
{"icon-name":"sign-kobo-alt"},
{"icon-name":"sign-steem-alt"},
{"icon-name":"sign-steller-alt"},
{"icon-name":"sign-stripe-fulll"},
{"icon-name":"sign-thb-alt"},
{"icon-name":"sign-trx-alt"},
{"icon-name":"sign-try-alt"},
{"icon-name":"sign-usd-alt"},
{"icon-name":"sign-usd-alt2"},
{"icon-name":"sign-usdc-alt"},
{"icon-name":"sign-usdt-alt"},
{"icon-name":"sign-visa-alt"},
{"icon-name":"sign-vnd-alt"},
{"icon-name":"sign-waves-alt"},
{"icon-name":"sign-xem-alt"},
{"icon-name":"sign-xrp-new-alt"},
{"icon-name":"sign-xrp-old-alt"},
{"icon-name":"sign-zcash-alt"},
{"icon-name":"chevron-left"},
{"icon-name":"chevron-right"},
{"icon-name":"chevron-up"},
{"icon-name":"chevron-down"},
{"icon-name":"chevron-left-round"},
{"icon-name":"chevron-right-round"},
{"icon-name":"chevron-up-round"},
{"icon-name":"chevron-down-round"},
{"icon-name":"chevron-left-round-fill"},
{"icon-name":"chevron-right-round-fill"},
{"icon-name":"chevron-up-round-fill"},
{"icon-name":"chevron-down-round-fill"},
{"icon-name":"chevron-left-c"},
{"icon-name":"chevron-right-c"},
{"icon-name":"chevron-up-c"},
{"icon-name":"chevron-down-c"},
{"icon-name":"chevron-left-fill-c"},
{"icon-name":"chevron-right-fill-c"},
{"icon-name":"chevron-up-fill-c"},
{"icon-name":"chevron-down-fill-c"},
{"icon-name":"chevron-left-circle"},
{"icon-name":"chevron-right-circle"},
{"icon-name":"chevron-up-circle"},
{"icon-name":"chevron-down-circle"},
{"icon-name":"chevron-left-circle-fill"},
{"icon-name":"chevron-right-circle-fill"},
{"icon-name":"chevron-up-circle-fill"},
{"icon-name":"chevron-down-circle-fill"},
{"icon-name":"caret-left"},
{"icon-name":"caret-right"},
{"icon-name":"caret-up"},
{"icon-name":"caret-down"},
{"icon-name":"caret-left-fill"},
{"icon-name":"caret-right-fill"},
{"icon-name":"caret-up-fill"},
{"icon-name":"caret-down-fill"},
{"icon-name":"sort"},
{"icon-name":"sort-up"},
{"icon-name":"sort-down"},
{"icon-name":"sort-fill"},
{"icon-name":"sort-up-fill"},
{"icon-name":"sort-down-fill"},
{"icon-name":"sort-v"},
{"icon-name":"swap-v"},
{"icon-name":"swap"},
{"icon-name":"arrow-left-round"},
{"icon-name":"arrow-right-round"},
{"icon-name":"arrow-up-round"},
{"icon-name":"arrow-down-round"},
{"icon-name":"arrow-left-round-fill"},
{"icon-name":"arrow-right-round-fill"},
{"icon-name":"arrow-up-round-fill"},
{"icon-name":"arrow-down-round-fill"},
{"icon-name":"arrow-left-c"},
{"icon-name":"arrow-right-c"},
{"icon-name":"arrow-up-c"},
{"icon-name":"arrow-down-c"},
{"icon-name":"arrow-left-fill-c"},
{"icon-name":"arrow-right-fill-c"},
{"icon-name":"arrow-up-fill-c"},
{"icon-name":"arrow-down-fill-c"},
{"icon-name":"arrow-left-circle"},
{"icon-name":"arrow-right-circle"},
{"icon-name":"arrow-up-circle"},
{"icon-name":"arrow-down-circle"},
{"icon-name":"arrow-left-circle-fill"},
{"icon-name":"arrow-up-circle-fill"},
{"icon-name":"arrow-down-circle-fill"},
{"icon-name":"arrow-right-circle-fill"},
{"icon-name":"chevrons-left"},
{"icon-name":"chevrons-right"},
{"icon-name":"chevrons-up"},
{"icon-name":"chevrons-down"},
{"icon-name":"first"},
{"icon-name":"last"},
{"icon-name":"back-ios"},
{"icon-name":"forward-ios"},
{"icon-name":"upword-ios"},
{"icon-name":"downward-ios"},
{"icon-name":"back-alt"},
{"icon-name":"forward-alt"},
{"icon-name":"upword-alt"},
{"icon-name":"downward-alt"},
{"icon-name":"back-alt-fill"},
{"icon-name":"forward-alt-fill"},
{"icon-name":"upword-alt-fill"},
{"icon-name":"downward-alt-fill"},
{"icon-name":"arrow-long-left"},
{"icon-name":"arrow-long-right"},
{"icon-name":"arrow-long-up"},
{"icon-name":"arrow-long-down"},
{"icon-name":"arrow-left"},
{"icon-name":"arrow-right"},
{"icon-name":"arrow-up"},
{"icon-name":"arrow-down"},
{"icon-name":"arrow-up-left"},
{"icon-name":"arrow-up-right"},
{"icon-name":"arrow-down-left"},
{"icon-name":"arrow-down-right"},
{"icon-name":"arrow-to-left"},
{"icon-name":"arrow-to-right"},
{"icon-name":"arrow-to-up"},
{"icon-name":"arrow-to-down"},
{"icon-name":"arrow-from-left"},
{"icon-name":"arrow-from-right"},
{"icon-name":"arrow-from-up"},
{"icon-name":"arrow-from-down"},
{"icon-name":"curve-down-left"},
{"icon-name":"curve-up-right"},
{"icon-name":"curve-up-left"},
{"icon-name":"curve-down-right"},
{"icon-name":"curve-left-up"},
{"icon-name":"curve-right-up"},
{"icon-name":"curve-left-down"},
{"icon-name":"curve-right-down"},
{"icon-name":"back-arrow"},
{"icon-name":"forward-arrow"},
{"icon-name":"back-arrow-fill"},
{"icon-name":"forward-arrow-fill"},
{"icon-name":"navigate"},
{"icon-name":"navigate-up"},
{"icon-name":"navigate-fill"},
{"icon-name":"navigate-up-fill"},
{"icon-name":"send"},
{"icon-name":"send-alt"},
{"icon-name":"unfold-less"},
{"icon-name":"unfold-more"},
{"icon-name":"exchange-v"},
{"icon-name":"exchange"},
{"icon-name":"expand"},
{"icon-name":"shrink"},
{"icon-name":"focus"},
{"icon-name":"maximize"},
{"icon-name":"minimize"},
{"icon-name":"maximize-alt"},
{"icon-name":"minimize-alt"},
{"icon-name":"shuffle"},
{"icon-name":"cross-sm"},
{"icon-name":"cross"},
{"icon-name":"cross-round"},
{"icon-name":"cross-circle"},
{"icon-name":"cross-c"},
{"icon-name":"cross-round-fill"},
{"icon-name":"cross-circle-fill"},
{"icon-name":"cross-fill-c"},
{"icon-name":"na"},
{"icon-name":"check"},
{"icon-name":"check-thick"},
{"icon-name":"done"},
{"icon-name":"check-round"},
{"icon-name":"check-circle"},
{"icon-name":"check-c"},
{"icon-name":"check-round-fill"},
{"icon-name":"check-circle-fill"},
{"icon-name":"check-fill-c"},
{"icon-name":"check-circle-cut"},
{"icon-name":"check-round-cut"},
{"icon-name":"bullet"},
{"icon-name":"circle"},
{"icon-name":"square"},
{"icon-name":"square-c"},
{"icon-name":"bullet-fill"},
{"icon-name":"circle-fill"},
{"icon-name":"square-fill"},
{"icon-name":"square-fill-c"},
{"icon-name":"plus-sm"},
{"icon-name":"minus-sm"},
{"icon-name":"plus"},
{"icon-name":"minus"},
{"icon-name":"plus-round"},
{"icon-name":"minus-round"},
{"icon-name":"plus-circle"},
{"icon-name":"minus-circle"},
{"icon-name":"plus-c"},
{"icon-name":"minus-c"},
{"icon-name":"plus-round-fill"},
{"icon-name":"plus-circle-fill"},
{"icon-name":"minus-round-fill"},
{"icon-name":"minus-circle-fill"},
{"icon-name":"plus-fill-c"},
{"icon-name":"minus-fill-c"},
{"icon-name":"plus-medi"},
{"icon-name":"plus-medi-fill"},
{"icon-name":"equal-sm"},
{"icon-name":"equal"},
{"icon-name":"calc"},
{"icon-name":"search"},
{"icon-name":"zoom-out"},
{"icon-name":"zoom-in"},
{"icon-name":"play"},
{"icon-name":"play-fill"},
{"icon-name":"play-circle"},
{"icon-name":"play-circle-fill"},
{"icon-name":"pause"},
{"icon-name":"pause-fill"},
{"icon-name":"pause-circle"},
{"icon-name":"pause-circle-fill"},
{"icon-name":"stop"},
{"icon-name":"stop-fill"},
{"icon-name":"stop-circle"},
{"icon-name":"stop-circle-fill"},
{"icon-name":"rewind"},
{"icon-name":"forward"},
{"icon-name":"rewind-fill"},
{"icon-name":"forward-fill"},
{"icon-name":"step-back"},
{"icon-name":"step-forward"},
{"icon-name":"vol-off"},
{"icon-name":"vol-no"},
{"icon-name":"vol-half"},
{"icon-name":"vol"},
{"icon-name":"mic"},
{"icon-name":"mic-off"},
{"icon-name":"video"},
{"icon-name":"video-off"},
{"icon-name":"video-fill"},
{"icon-name":"loader"},
{"icon-name":"power"},
{"icon-name":"signout"},
{"icon-name":"signin"},
{"icon-name":"upload"},
{"icon-name":"download"},
{"icon-name":"alert-circle"},
{"icon-name":"alert"},
{"icon-name":"caution"},
{"icon-name":"report"},
{"icon-name":"alert-c"},
{"icon-name":"alert-circle-fill"},
{"icon-name":"alert-fill"},
{"icon-name":"caution-fill"},
{"icon-name":"report-fill"},
{"icon-name":"alert-fill-c"},
{"icon-name":"info-i"},
{"icon-name":"info"},
{"icon-name":"info-fill"},
{"icon-name":"help"},
{"icon-name":"help-fill"},
{"icon-name":"archived"},
{"icon-name":"archive"},
{"icon-name":"unarchive"},
{"icon-name":"archived-fill"},
{"icon-name":"archive-fill"},
{"icon-name":"unarchive-fill"},
{"icon-name":"bag"},
{"icon-name":"bag-fill"},
{"icon-name":"bell"},
{"icon-name":"bell-off"},
{"icon-name":"bell-fill"},
{"icon-name":"bell-off-fill"},
{"icon-name":"wifi"},
{"icon-name":"wifi-off"},
{"icon-name":"live"},
{"icon-name":"signal"},
{"icon-name":"bluetooth"},
{"icon-name":"blank-alt"},
{"icon-name":"blank"},
{"icon-name":"blankf-fill"},
{"icon-name":"block-over"},
{"icon-name":"book-read"},
{"icon-name":"book"},
{"icon-name":"book-fill"},
{"icon-name":"bulb-fill"},
{"icon-name":"bulb"},
{"icon-name":"calendar-alt-fill"},
{"icon-name":"calendar-alt"},
{"icon-name":"calendar-booking-fill"},
{"icon-name":"calendar-booking"},
{"icon-name":"calendar-check-fill"},
{"icon-name":"calendar-check"},
{"icon-name":"calendar-fill"},
{"icon-name":"calendar"},
{"icon-name":"calender-date-fill"},
{"icon-name":"calender-date"},
{"icon-name":"call"},
{"icon-name":"call-alt"},
{"icon-name":"call-alt-fill"},
{"icon-name":"call-fill"},
{"icon-name":"camera-fill"},
{"icon-name":"camera"},
{"icon-name":"capsule"},
{"icon-name":"capsule-fill"},
{"icon-name":"cards"},
{"icon-name":"cards-fill"},
{"icon-name":"cart"},
{"icon-name":"cart-fill"},
{"icon-name":"cc"},
{"icon-name":"cc-alt"},
{"icon-name":"cc-alt2"},
{"icon-name":"cc-secure"},
{"icon-name":"cc-new"},
{"icon-name":"cc-off"},
{"icon-name":"cc-fill"},
{"icon-name":"cc-alt-fill"},
{"icon-name":"cc-alt2-fill"},
{"icon-name":"cc-secure-fill"},
{"icon-name":"msg-circle"},
{"icon-name":"chat-circle"},
{"icon-name":"msg"},
{"icon-name":"chat"},
{"icon-name":"question-alt"},
{"icon-name":"question"},
{"icon-name":"msg-circle-fill"},
{"icon-name":"chat-circle-fill"},
{"icon-name":"msg-fill"},
{"icon-name":"chat-fill"},
{"icon-name":"clip-h"},
{"icon-name":"clip-v"},
{"icon-name":"clip"},
{"icon-name":"link-alt"},
{"icon-name":"unlink"},
{"icon-name":"unlink-alt"},
{"icon-name":"link-h"},
{"icon-name":"link-v"},
{"icon-name":"link"},
{"icon-name":"clipboard"},
{"icon-name":"clipboad-check"},
{"icon-name":"clipboard-fill"},
{"icon-name":"clipboad-check-fill"},
{"icon-name":"clock"},
{"icon-name":"clock-fill"},
{"icon-name":"cloud"},
{"icon-name":"upload-cloud"},
{"icon-name":"download-cloud"},
{"icon-name":"cloud-fill"},
{"icon-name":"contact"},
{"icon-name":"contact-fill"},
{"icon-name":"coffee"},
{"icon-name":"coffee-fill"},
{"icon-name":"box-view"},
{"icon-name":"col-view"},
{"icon-name":"sidebar"},
{"icon-name":"layout"},
{"icon-name":"table-view"},
{"icon-name":"layout2"},
{"icon-name":"row-view"},
{"icon-name":"dot-box"},
{"icon-name":"layout-fill"},
{"icon-name":"box-view-fill"},
{"icon-name":"sidebar-fill"},
{"icon-name":"table-view-fill"},
{"icon-name":"dot-box-fill"},
{"icon-name":"template"},
{"icon-name":"browser"},
{"icon-name":"toolbar"},
{"icon-name":"browser-fill"},
{"icon-name":"toolbar-fill"},
{"icon-name":"template-fill"},
{"icon-name":"box"},
{"icon-name":"package"},
{"icon-name":"layer"},
{"icon-name":"layers"},
{"icon-name":"panel"},
{"icon-name":"server"},
{"icon-name":"layer-fill"},
{"icon-name":"layers-fill"},
{"icon-name":"package-fill"},
{"icon-name":"panel-fill"},
{"icon-name":"server-fill"},
{"icon-name":"color-palette"},
{"icon-name":"color-palette-fill"},
{"icon-name":"copy"},
{"icon-name":"copy-fill"},
{"icon-name":"crop-alt"},
{"icon-name":"crop"},
{"icon-name":"target"},
{"icon-name":"crosshair"},
{"icon-name":"crosshair-fill"},
{"icon-name":"db-fill"},
{"icon-name":"db"},
{"icon-name":"hard-drive"},
{"icon-name":"cpu"},
{"icon-name":"disk"},
{"icon-name":"pen"},
{"icon-name":"edit-alt"},
{"icon-name":"pen-fill"},
{"icon-name":"edit-alt-fill"},
{"icon-name":"pen-alt-fill"},
{"icon-name":"edit-fill"},
{"icon-name":"edit"},
{"icon-name":"external-alt"},
{"icon-name":"external"},
{"icon-name":"eye-alt"},
{"icon-name":"eye-alt-fill"},
{"icon-name":"eye"},
{"icon-name":"eye-fill"},
{"icon-name":"eye-off"},
{"icon-name":"eye-off-fill"},
{"icon-name":"file"},
{"icon-name":"file-minus"},
{"icon-name":"file-plus"},
{"icon-name":"file-remove"},
{"icon-name":"file-check"},
{"icon-name":"file-code"},
{"icon-name":"file-docs"},
{"icon-name":"file-img"},
{"icon-name":"file-doc"},
{"icon-name":"file-pdf"},
{"icon-name":"file-xls"},
{"icon-name":"file-zip"},
{"icon-name":"file-download"},
{"icon-name":"file-text"},
{"icon-name":"files"},
{"icon-name":"file-fill"},
{"icon-name":"file-minus-fill"},
{"icon-name":"file-plus-fill"},
{"icon-name":"file-remove-fill"},
{"icon-name":"file-check-fill"},
{"icon-name":"file-text-fill"},
{"icon-name":"files-fill"},
{"icon-name":"folder"},
{"icon-name":"folder-minus"},
{"icon-name":"folder-plus"},
{"icon-name":"folder-remove"},
{"icon-name":"folder-check"},
{"icon-name":"folder-list"},
{"icon-name":"folders"},
{"icon-name":"folder-fill"},
{"icon-name":"folders-fill"},
{"icon-name":"filter-alt"},
{"icon-name":"sort-line"},
{"icon-name":"filter-fill"},
{"icon-name":"filter"},
{"icon-name":"flag"},
{"icon-name":"flag-fill"},
{"icon-name":"notify"},
{"icon-name":"dashboard"},
{"icon-name":"dashboard-fill"},
{"icon-name":"grid-sq"},
{"icon-name":"grid"},
{"icon-name":"grid-c"},
{"icon-name":"grid-alt"},
{"icon-name":"grid-plus"},
{"icon-name":"grid-add-c"},
{"icon-name":"grid-fill"},
{"icon-name":"grid-fill-c"},
{"icon-name":"grid-alt-fill"},
{"icon-name":"grid-plus-fill"},
{"icon-name":"grid-add-fill-c"},
{"icon-name":"grid-box-alt-fill"},
{"icon-name":"grid-box-alt"},
{"icon-name":"grid-box"},
{"icon-name":"grid-box-fill"},
{"icon-name":"grid-line"},
{"icon-name":"menu-alt-left"},
{"icon-name":"menu-alt-r"},
{"icon-name":"menu-alt"},
{"icon-name":"menu-center"},
{"icon-name":"menu-left"},
{"icon-name":"menu-right"},
{"icon-name":"menu"},
{"icon-name":"trend-up"},
{"icon-name":"trend-down"},
{"icon-name":"line-chart-down"},
{"icon-name":"line-chart-up"},
{"icon-name":"line-chart"},
{"icon-name":"bar-chart"},
{"icon-name":"bar-chart-alt"},
{"icon-name":"chart-up"},
{"icon-name":"chart-down"},
{"icon-name":"growth"},
{"icon-name":"growth-fill"},
{"icon-name":"bar-chart-fill"},
{"icon-name":"bar-c"},
{"icon-name":"bar-fill-c"},
{"icon-name":"pie"},
{"icon-name":"pie-alt"},
{"icon-name":"pie-fill"},
{"icon-name":"activity"},
{"icon-name":"activity-alt"},
{"icon-name":"activity-round"},
{"icon-name":"activity-round-fill"},
{"icon-name":"meter"},
{"icon-name":"speed"},
{"icon-name":"happy"},
{"icon-name":"sad"},
{"icon-name":"meh"},
{"icon-name":"happyf-fill"},
{"icon-name":"sad-fill"},
{"icon-name":"meh-fill"},
{"icon-name":"home"},
{"icon-name":"home-alt"},
{"icon-name":"home-fill"},
{"icon-name":"img"},
{"icon-name":"img-fill"},
{"icon-name":"inbox"},
{"icon-name":"inbox-in"},
{"icon-name":"inbox-out"},
{"icon-name":"inbox-fill"},
{"icon-name":"inbox-in-fill"},
{"icon-name":"inbox-out-fill"},
{"icon-name":"link-group"},
{"icon-name":"lock"},
{"icon-name":"lock-alt"},
{"icon-name":"lock-fill"},
{"icon-name":"lock-alt-fill"},
{"icon-name":"unlock"},
{"icon-name":"unlock-fill"},
{"icon-name":"mail"},
{"icon-name":"emails"},
{"icon-name":"mail-fill"},
{"icon-name":"emails-fill"},
{"icon-name":"map-pin"},
{"icon-name":"location"},
{"icon-name":"map"},
{"icon-name":"map-pin-fill"},
{"icon-name":"list"},
{"icon-name":"list-ol"},
{"icon-name":"align-center"},
{"icon-name":"align-justify"},
{"icon-name":"align-left"},
{"icon-name":"align-right"},
{"icon-name":"list-check"},
{"icon-name":"list-round"},
{"icon-name":"card-view"},
{"icon-name":"list-fill"},
{"icon-name":"save"},
{"icon-name":"save-fill"},
{"icon-name":"move"},
{"icon-name":"scissor"},
{"icon-name":"text"},
{"icon-name":"text-a"},
{"icon-name":"bold"},
{"icon-name":"italic"},
{"icon-name":"underline"},
{"icon-name":"percent"},
{"icon-name":"at"},
{"icon-name":"hash"},
{"icon-name":"code"},
{"icon-name":"code-download"},
{"icon-name":"terminal"},
{"icon-name":"cmd"},
{"icon-name":"sun"},
{"icon-name":"sun-fill"},
{"icon-name":"moon-fill"},
{"icon-name":"moon"},
{"icon-name":"light"},
{"icon-name":"light-fill"},
{"icon-name":"more-v"},
{"icon-name":"more-h"},
{"icon-name":"more-h-alt"},
{"icon-name":"more-v-alt"},
{"icon-name":"music"},
{"icon-name":"movie"},
{"icon-name":"offer"},
{"icon-name":"offer-fill"},
{"icon-name":"opt-alt"},
{"icon-name":"opt"},
{"icon-name":"opt-dot-alt"},
{"icon-name":"opt-dot"},
{"icon-name":"opt-dot-fill"},
{"icon-name":"opt-alt-fill"},
{"icon-name":"user-alt"},
{"icon-name":"user-alt-fill"},
{"icon-name":"user"},
{"icon-name":"users"},
{"icon-name":"user-add"},
{"icon-name":"user-remove"},
{"icon-name":"user-check"},
{"icon-name":"user-cross"},
{"icon-name":"account-setting"},
{"icon-name":"account-setting-alt"},
{"icon-name":"user-list"},
{"icon-name":"user-fill"},
{"icon-name":"users-fill"},
{"icon-name":"user-add-fill"},
{"icon-name":"user-remove-fill"},
{"icon-name":"user-check-fill"},
{"icon-name":"user-cross-fill"},
{"icon-name":"account-setting-fill"},
{"icon-name":"user-list-fill"},
{"icon-name":"user-circle"},
{"icon-name":"user-circle-fill"},
{"icon-name":"user-c"},
{"icon-name":"user-fill-c"},
{"icon-name":"user-round"},
{"icon-name":"printer"},
{"icon-name":"printer-fill"},
{"icon-name":"laptop"},
{"icon-name":"monitor"},
{"icon-name":"tablet"},
{"icon-name":"mobile"},
{"icon-name":"undo"},
{"icon-name":"redo"},
{"icon-name":"reload-alt"},
{"icon-name":"reload"},
{"icon-name":"regen-alt"},
{"icon-name":"regen"},
{"icon-name":"invest"},
{"icon-name":"history"},
{"icon-name":"update"},
{"icon-name":"repeat"},
{"icon-name":"repeat-v"},
{"icon-name":"tranx"},
{"icon-name":"reply-all"},
{"icon-name":"reply"},
{"icon-name":"reply-fill"},
{"icon-name":"reply-all-fill"},
{"icon-name":"notes"},
{"icon-name":"note-add"},
{"icon-name":"notes-alt"},
{"icon-name":"article"},
{"icon-name":"text-rich"},
{"icon-name":"todo"},
{"icon-name":"report-profit"},
{"icon-name":"reports-alt"},
{"icon-name":"reports"},
{"icon-name":"task"},
{"icon-name":"note-add-c"},
{"icon-name":"task-c"},
{"icon-name":"todo-fill"},
{"icon-name":"note-add-fill-c"},
{"icon-name":"task-fill-c"},
{"icon-name":"scan-fill"},
{"icon-name":"scan"},
{"icon-name":"qr"},
{"icon-name":"money"},
{"icon-name":"coins"},
{"icon-name":"coin"},
{"icon-name":"coin-alt"},
{"icon-name":"coin-alt-fill"},
{"icon-name":"setting-alt-fill"},
{"icon-name":"setting-alt"},
{"icon-name":"setting-fill"},
{"icon-name":"setting"},
{"icon-name":"share-alt"},
{"icon-name":"share-fill"},
{"icon-name":"share"},
{"icon-name":"network"},
{"icon-name":"rss"},
{"icon-name":"shield"},
{"icon-name":"shield-star"},
{"icon-name":"shield-check"},
{"icon-name":"shield-alert"},
{"icon-name":"shield-off"},
{"icon-name":"security"},
{"icon-name":"policy"},
{"icon-name":"shield-alert-fill"},
{"icon-name":"shield-check-fill"},
{"icon-name":"shield-fill"},
{"icon-name":"shield-half"},
{"icon-name":"shield-star-fill"},
{"icon-name":"policy-fill"},
{"icon-name":"spark"},
{"icon-name":"spark-off"},
{"icon-name":"spark-fill"},
{"icon-name":"spark-off-fill"},
{"icon-name":"wallet"},
{"icon-name":"wallet-alt"},
{"icon-name":"wallet-in"},
{"icon-name":"wallet-out"},
{"icon-name":"wallet-saving"},
{"icon-name":"wallet-fill"},
{"icon-name":"star"},
{"icon-name":"star-half"},
{"icon-name":"star-half-fill"},
{"icon-name":"star-fill"},
{"icon-name":"star-round"},
{"icon-name":"heart"},
{"icon-name":"heart-fill"},
{"icon-name":"swap-alt-fill"},
{"icon-name":"swap-alt"},
{"icon-name":"thumbs-down"},
{"icon-name":"thumbs-up"},
{"icon-name":"tag"},
{"icon-name":"tag-alt"},
{"icon-name":"tags"},
{"icon-name":"tag-fill"},
{"icon-name":"tag-alt-fill"},
{"icon-name":"tags-fill"},
{"icon-name":"bookmark"},
{"icon-name":"bookmark-fill"},
{"icon-name":"label"},
{"icon-name":"label-fill"},
{"icon-name":"piority"},
{"icon-name":"piority-fill"},
{"icon-name":"label-alt"},
{"icon-name":"label-alt-fill"},
{"icon-name":"ticket-alt"},
{"icon-name":"ticket"},
{"icon-name":"ticket-minus"},
{"icon-name":"ticket-plus"},
{"icon-name":"ticket-alt-fill"},
{"icon-name":"ticket-fill"},
{"icon-name":"ticket-minus-fill"},
{"icon-name":"ticket-plus-fill"},
{"icon-name":"toggle-off"},
{"icon-name":"toggle-on"},
{"icon-name":"trash-alt"},
{"icon-name":"trash-empty"},
{"icon-name":"trash"},
{"icon-name":"trash-fill"},
{"icon-name":"trash-empty-fill"},
{"icon-name":"delete-fill"},
{"icon-name":"delete"},
{"icon-name":"alarm-alt"},
{"icon-name":"alarm"},
{"icon-name":"bugs"},
{"icon-name":"building"},
{"icon-name":"building-fill"},
{"icon-name":"headphone"},
{"icon-name":"headphone-fill"},
{"icon-name":"aperture"},
{"icon-name":"help-alt"},
{"icon-name":"award"},
{"icon-name":"briefcase"},
{"icon-name":"gift"},
{"icon-name":"globe"},
{"icon-name":"umbrela"},
{"icon-name":"truck"},
{"icon-name":"sign-usd"},
{"icon-name":"sign-dollar"},
{"icon-name":"sign-mxn"},
{"icon-name":"sign-sgd"},
{"icon-name":"sign-euro"},
{"icon-name":"sign-eur"},
{"icon-name":"sign-gbp"},
{"icon-name":"sign-pound"},
{"icon-name":"sign-thb"},
{"icon-name":"sign-inr"},
{"icon-name":"sign-jpy"},
{"icon-name":"sign-yen"},
{"icon-name":"sign-cny"},
{"icon-name":"sign-kobo"},
{"icon-name":"sign-chf"},
{"icon-name":"sign-vnd"},
{"icon-name":"sign-php"},
{"icon-name":"sign-brl"},
{"icon-name":"sign-idr"},
{"icon-name":"sign-czk"},
{"icon-name":"sign-hkd"},
{"icon-name":"sign-kr"},
{"icon-name":"sign-dkk"},
{"icon-name":"sign-nok"},
{"icon-name":"sign-sek"},
{"icon-name":"sign-rub"},
{"icon-name":"sign-myr"},
{"icon-name":"sign-pln"},
{"icon-name":"sign-try"},
{"icon-name":"sign-waves"},
{"icon-name":"waves"},
{"icon-name":"sign-trx"},
{"icon-name":"tron"},
{"icon-name":"sign-xem"},
{"icon-name":"nem"},
{"icon-name":"sign-mxr"},
{"icon-name":"monero"},
{"icon-name":"sign-usdc"},
{"icon-name":"sign-steller"},
{"icon-name":"sign-steem"},
{"icon-name":"sign-usdt"},
{"icon-name":"tether"},
{"icon-name":"sign-btc"},
{"icon-name":"bitcoin"},
{"icon-name":"sign-bch"},
{"icon-name":"bitcoin-cash"},
{"icon-name":"sign-bnb"},
{"icon-name":"binance"},
{"icon-name":"sign-ada"},
{"icon-name":"sign-zcash"},
{"icon-name":"sign-eth"},
{"icon-name":"ethereum"},
{"icon-name":"sign-dash"},
{"icon-name":"dash"},
{"icon-name":"sign-xrp-old"},
{"icon-name":"ripple-old"},
{"icon-name":"sign-eos"},
{"icon-name":"eos"},
{"icon-name":"sign-xrp"},
{"icon-name":"ripple"},
{"icon-name":"american-express"},
{"icon-name":"jcb"},
{"icon-name":"cc-jcb"},
{"icon-name":"mc"},
{"icon-name":"cc-mc"},
{"icon-name":"discover"},
{"icon-name":"cc-discover"},
{"icon-name":"visa"},
{"icon-name":"cc-visa"},
{"icon-name":"cc-paypal"},
{"icon-name":"cc-stripe"},
{"icon-name":"amazon-pay"},
{"icon-name":"amazon-pay-fill"},
{"icon-name":"google-pay"},
{"icon-name":"google-pay-fill"},
{"icon-name":"apple-pay"},
{"icon-name":"apple-pay-fill"},
{"icon-name":"angular"},
{"icon-name":"react"},
{"icon-name":"laravel"},
{"icon-name":"html5"},
{"icon-name":"css3-fill"},
{"icon-name":"css3"},
{"icon-name":"js"},
{"icon-name":"php"},
{"icon-name":"python"},
{"icon-name":"bootstrap"},
{"icon-name":"ebay"},
{"icon-name":"google-wallet"},
{"icon-name":"google-drive"},
{"icon-name":"google-play-store"},
{"icon-name":"android"},
{"icon-name":"blogger-fill"},
{"icon-name":"blogger"},
{"icon-name":"hangout"},
{"icon-name":"apple-store"},
{"icon-name":"apple-store-ios"},
{"icon-name":"stripe"},
{"icon-name":"apple"},
{"icon-name":"microsoft"},
{"icon-name":"windows"},
{"icon-name":"amazon"},
{"icon-name":"paypal-alt"},
{"icon-name":"airbnb"},
{"icon-name":"adobe"},
{"icon-name":"mailchimp"},
{"icon-name":"dropbox"},
{"icon-name":"digital-ocean"},
{"icon-name":"slack"},
{"icon-name":"slack-hash"},
{"icon-name":"stack-overflow"},
{"icon-name":"soundcloud"},
{"icon-name":"blackberry"},
{"icon-name":"spotify"},
{"icon-name":"kickstarter"},
{"icon-name":"houzz"},
{"icon-name":"vine"},
{"icon-name":"yelp"},
{"icon-name":"yoast"},
{"icon-name":"envato"},
{"icon-name":"wordpress"},
{"icon-name":"wp"},
{"icon-name":"wordpress-fill"},
{"icon-name":"elementor"},
{"icon-name":"joomla"},
{"icon-name":"megento"},
{"icon-name":"git"},
{"icon-name":"github"},
{"icon-name":"github-round"},
{"icon-name":"github-circle"},
{"icon-name":"dribbble"},
{"icon-name":"dribbble-round"},
{"icon-name":"behance"},
{"icon-name":"behance-fill"},
{"icon-name":"flickr"},
{"icon-name":"flickr-round"},
{"icon-name":"medium"},
{"icon-name":"medium-round"},
{"icon-name":"reddit"},
{"icon-name":"reddit-round"},
{"icon-name":"reddit-circle"},
{"icon-name":"google"},
{"icon-name":"facebook-f"},
{"icon-name":"facebook-fill"},
{"icon-name":"facebook-circle"},
{"icon-name":"instagram"},
{"icon-name":"instagram-round"},
{"icon-name":"linkedin"},
{"icon-name":"linkedin-round"},
{"icon-name":"twitter"},
{"icon-name":"twitter-round"},
{"icon-name":"pinterest"},
{"icon-name":"pinterest-round"},
{"icon-name":"pinterest-circle"},
{"icon-name":"tumblr"},
{"icon-name":"tumblr-round"},
{"icon-name":"skype"},
{"icon-name":"viber"},
{"icon-name":"whatsapp"},
{"icon-name":"whatsapp-round"},
{"icon-name":"snapchat"},
{"icon-name":"snapchat-fill"},
{"icon-name":"telegram"},
{"icon-name":"telegram-circle"},
{"icon-name":"youtube-line"},
{"icon-name":"youtube"},
{"icon-name":"youtube-fill"},
{"icon-name":"youtube-round"},
{"icon-name":"vimeo"},
{"icon-name":"vimeo-fill"}
];

export const svgData = [
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="68"
          height="66"
          x="13"
          y="16"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M24 82H35V87H24z"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M60 82H71V87H60z"
        ></path>
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M47 70.15S61.89 62.49 61.89 51V37.6L47 31.85 32.11 37.6V51C32.11 62.49 47 70.15 47 70.15z"
        ></path>
        <path
          fill="#6576ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M41.56 48h10.88A1.6 1.6 0 0154 49.59v5.73A1.6 1.6 0 0152.44 57H41.56A1.6 1.6 0 0140 55.32v-5.73A1.6 1.6 0 0141.56 48z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M43 48v-3a4 4 0 018 0v3"
        ></path>
        <circle
          cx="46.67"
          cy="52.79"
          r="0.91"
          fill="#fff"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <circle
          cx="23"
          cy="17"
          r="14"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <circle cx="23" cy="17" r="10.5" fill="#e3e7fe"></circle>
        <path
          fill="#6576ff"
          d="M28.46 20.47l-4.41-4.41a3.4 3.4 0 00.26-1.31A3.34 3.34 0 1021 18.1a3.41 3.41 0 001.31-.27l1.44 1.45a.33.33 0 00.23.09h.79v.79a.32.32 0 00.09.23.27.27 0 00.23.08h.79v.79a.31.31 0 00.09.22.29.29 0 00.22.09h.79v.79a.3.3 0 00.09.24.32.32 0 00.21.08l1.21-.14a.3.3 0 00.27-.33l-.13-1.48z"
        ></path>
        <path
          fill="#fff"
          d="M20.56 14.09a1 1 0 01-1.34 0 1 1 0 010-1.35 1 1 0 111.34 1.35zM23.72 16.39l3.79 3.79a.22.22 0 010 .32.24.24 0 01-.32 0l-3.75-3.76z"
        ></path>
        <path fill="none" d="M17.32 11.6H28.32V22.6H17.32z"></path>
      </svg>
    ),
    name: "Account Secure",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="55"
          height="68"
          x="12"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M78 15.8l-5.72 6.33L66.7 16l-5.33 5.91-5.82-6.46L49.67 22l-5.54-6.2-5.49 6.09L33 15.62 27.27 22l-6.16-6.83L21 15v64c0 3.33 2.43 6 5.43 6h46.14c3 0 5.43-2.69 5.43-6V15.76s-.06.03 0 .04z"
        ></path>
        <circle
          cx="49.5"
          cy="35.5"
          r="2.5"
          fill="#c4cefe"
          stroke="#c4cefe"
          strokeMiterlimit="10"
          strokeWidth="2"
        ></circle>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M71 50L28 49.86"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M71 56L28 55.86"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M71 61L28 60.86"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M28 45L38 45"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M61 45L71 45"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M28 76L41 76"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M61 76L69 76"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M59 66L69 66"
        ></path>
      </svg>
    ),
    name: "Bills",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="55"
          height="39"
          x="9"
          y="21"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M17 44L25 44"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M30 44L38 44"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 44L50 44"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M17 50L36 50"
        ></path>
        <rect width="15" height="8" x="16" y="31" fill="#c4cefe" rx="1" ry="1"></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M76.79 72.87L32.22 86.73a6 6 0 01-7.47-4L17 57.71a6 6 0 014-7.51l44.52-13.86a6 6 0 017.48 4l7.73 25.06a6 6 0 01-3.94 7.47z"
        ></path>
        <path fill="#6576ff" d="M75.27 47.3L19.28 64.71 17.14 57.76 73.12 40.35 75.27 47.3z"></path>
        <path
          fill="#c4cefe"
          d="M30 77.65l-1.9-6.79a1 1 0 01.69-1.23l4.59-1.3a1 1 0 011.23.7l1.9 6.78a1 1 0 01-.67 1.19l-4.59 1.3a1 1 0 01-1.25-.65zM41.23 74.48l-1.9-6.78a1 1 0 01.67-1.23l4.58-1.3a1 1 0 011.23.69l1.9 6.78a1 1 0 01-.71 1.24l-4.58 1.29a1 1 0 01-1.19-.69zM52.43 71.32l-1.9-6.79a1 1 0 01.69-1.23l4.59-1.3a1 1 0 011.19.7l1.9 6.78a1 1 0 01-.69 1.23L53.66 72a1 1 0 01-1.23-.68z"
        ></path>
        <ellipse
          cx="55.46"
          cy="19.1"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="16.04"
          ry="16.1"
        ></ellipse>
        <ellipse cx="55.46" cy="19.1" fill="#e3e7fe" rx="12.11" ry="12.16"></ellipse>
        <text
          fill="#6576ff"
          fontFamily="Nunito-Black, Nunito Black"
          fontSize="16.12"
          transform="matrix(.99 0 0 1 50.7 23.72)"
        >
          $
        </text>
      </svg>
    ),
    name: "Card Credit",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M43.94 13.5H81a6 6 0 016 6v36a6 6 0 01-6 6H22a6 6 0 01-6-6v-36a6 6 0 016-6h21.94z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M38.94 20.5H76a6 6 0 016 6v36a6 6 0 01-6 6H17a6 6 0 01-6-6v-36a6 6 0 016-6h21.94z"
        ></path>
        <rect
          width="73"
          height="50"
          x="3"
          y="26.5"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <circle
          cx="64.5"
          cy="65"
          r="4.5"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        ></circle>
        <circle
          cx="59.5"
          cy="65"
          r="4.5"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        ></circle>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M11 32.5H24V41.5H11z"
        ></path>
        <rect width="2" height="5" x="10" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="16" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="23" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="30" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="37" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="43" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="50" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="57" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="2" height="5" x="64" y="47.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <path fill="#c4cefe" d="M65 32.5H69V38.5H65z"></path>
        <path fill="#c4cefe" d="M58 32.5H62V38.5H58z"></path>
        <path fill="#c4cefe" d="M51 32.5H55V38.5H51z"></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          d="M10.5 61L34.5 61"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          d="M10.5 68L17.5 68"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          d="M22.5 68L39.5 68"
        ></path>
      </svg>
    ),
    name: "Card Debit",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <circle
          cx="27.5"
          cy="39.5"
          r="4.5"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        ></circle>
        <circle
          cx="23.5"
          cy="39.5"
          r="4.5"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
        ></circle>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M19 67L37 67"
        ></path>
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M29.36 15h34.55A6.12 6.12 0 0170 21.14v32.72A6.12 6.12 0 0163.91 60H9.09A6.12 6.12 0 013 53.86V21.14A6.12 6.12 0 019.09 15h20.27z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34.36 20h34.55A6.12 6.12 0 0175 26.14v32.72A6.12 6.12 0 0168.91 65H14.09A6.12 6.12 0 018 58.86V26.14A6.12 6.12 0 0114.09 20h20.27z"
        ></path>
        <rect
          width="67"
          height="45"
          x="14"
          y="25"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M19 53L31 53"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 53L41 53"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M19 58L31 58"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 58L41 58"
        ></path>
        <path fill="#c4cefe" d="M71 31H75V37H71z"></path>
        <path fill="#c4cefe" d="M64 31H68V37H64z"></path>
        <path fill="#c4cefe" d="M58 31H62V37H58z"></path>
        <circle
          cx="72.5"
          cy="60.5"
          r="14.5"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <circle cx="73" cy="61" r="11" fill="#e3e7fe"></circle>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M70 59v-1.48a3 3 0 016 0V59"
        ></path>
        <path
          fill="#6576ff"
          d="M68.33 59h9.34A1.3 1.3 0 0179 60.27v4.46A1.3 1.3 0 0177.67 66h-9.34A1.3 1.3 0 0167 64.73v-4.46A1.3 1.3 0 0168.33 59z"
        ></path>
        <ellipse cx="73" cy="61.74" fill="#fff" rx="1.11" ry="1.12"></ellipse>
        <path fill="#fff" d="M72.5 62.38h1v1.5a.5.5 0 01-.******* 0 01-.5-.5v-1.5z"></path>
      </svg>
    ),
    name: "Card Secure",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M44 24H85V67H44z"
          transform="rotate(-180 64.5 45.5)"
        ></path>
        <path
          fill="#eff1ff"
          d="M71 37v-3.4a1.6 1.6 0 00-1.6-1.6H54.6a1.6 1.6 0 00-1.6 1.6v14.79A1.61 1.61 0 0054.61 50H69.4a1.6 1.6 0 001.6-1.6V37z"
        ></path>
        <path fill="none" stroke="#6576ff" strokeWidth="1.5" d="M60 42L60 45"></path>
        <path fill="none" stroke="#6576ff" strokeWidth="1.5" d="M58 46L56 46"></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
          d="M64 35H68V39H64z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
          d="M56 35H60V39H56z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="1.5"
          d="M64 42H68V46H64z"
        ></path>
        <path fill="none" stroke="#6576ff" strokeWidth="1.5" d="M58 42L56 42 56 44"></path>
        <path fill="#e3e7fe" d="M76 62H46v-9h30a2 2 0 012 2v5a2 2 0 01-2 2z"></path>
        <circle
          cx="70"
          cy="28"
          r="0.5"
          fill="none"
          stroke="#33d895"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <circle
          cx="75"
          cy="28"
          r="0.5"
          fill="none"
          stroke="#f4bd0e"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <circle
          cx="80"
          cy="28"
          r="0.5"
          fill="none"
          stroke="#eb6459"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <rect
          width="40"
          height="72"
          x="5"
          y="9"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="3"
          ry="3"
        ></rect>
        <path fill="#fff" d="M9 22H41V65H9z"></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M31 34L35 34 35 38"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M19 54L15 54 15 50"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M15 38L15 34 19 34"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 50L35 54 31 54"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M25 41L25 47"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M22 44L28 44"
        ></path>
        <circle
          cx="25"
          cy="74"
          r="3"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M23 15L28 15"
        ></path>
      </svg>
    ),
    name: "Code Scan",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="70"
          height="60"
          x="5"
          y="22"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path fill="#b3c2ff" d="M12 23h56a6 6 0 016 6v6H6v-6a6 6 0 016-6z"></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M5 35L75 35"
        ></path>
        <rect
          width="70"
          height="60"
          x="15"
          y="8"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path fill="#e3e7fe" d="M22 9h56a6 6 0 016 6v6H16v-6a6 6 0 016-6z"></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M15 22L85 22"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M61 15L68 15"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M74 15L78 15"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          d="M60.49 51.07L67.06 44.5 60.49 37.93"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          d="M41.51 37.93L34.94 44.5 41.51 51.07"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="3"
          d="M54.55 34.5L47.45 54.5"
        ></path>
      </svg>
    ),
    name: "Codes",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="36"
          height="62"
          x="3.5"
          y="14"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="2"
          ry="2"
        ></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M3.5 22L39.5 22"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M3.5 64L39.5 64"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M20.5 18L25.5 18"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M17.17 18L17.17 18"
        ></path>
        <circle
          cx="21.5"
          cy="70"
          r="2"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path fill="#eff1ff" d="M7.5 25H35.5V60H7.5z"></path>
        <rect width="4" height="6" x="10.5" y="40" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect width="4" height="6" x="16.5" y="40" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect width="4" height="6" x="22.5" y="40" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect width="4" height="6" x="28.5" y="40" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect
          width="36"
          height="62"
          x="50.5"
          y="14"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="2"
          ry="2"
        ></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M50.5 22L86.5 22"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M50.5 64L86.5 64"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M67.5 18L72.5 18"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M64.45 17.86L64.45 17.86"
        ></path>
        <circle
          cx="68.5"
          cy="70"
          r="2"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path fill="#eff1ff" d="M54.5 25H82.5V60H54.5z"></path>
        <rect width="4" height="6" x="57.5" y="39" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect width="4" height="6" x="63.5" y="39" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect width="4" height="6" x="69.5" y="39" fill="#c4cefe" rx="2" ry="2"></rect>
        <rect width="4" height="6" x="75.5" y="39" fill="#c4cefe" rx="2" ry="2"></rect>
        <ellipse
          cx="45.51"
          cy="44"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="15.18"
          ry="15"
        ></ellipse>
        <ellipse cx="45.51" cy="44" fill="#e3e7fe" rx="11.13" ry="11"></ellipse>
        <path
          fill="#6576ff"
          d="M46 50.92s5.5-2.77 5.5-6.92v-4.84L46 37.08l-5.5 2.08V44c0 4.15 5.5 6.92 5.5 6.92z"
        ></path>
        <path
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M48.04 42L44.56 46 42.98 44.18"
        ></path>
      </svg>
    ),
    name: "Connect Secure",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="53.97"
          height="69.95"
          x="5"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M51.66 15H22.4a7.22 7.22 0 00-7.4 7v56a7.21 7.21 0 007.41 7h39.15A7.2 7.2 0 0069 78V30.5z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68.96 30.98L51.97 30.91 51.97 15.99"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M23 34L44 34"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M23 42L57 42"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M23 50L57 50"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M23 58L32 58"
        ></path>
        <ellipse
          cx="61.1"
          cy="61.11"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="23.9"
          ry="23.89"
        ></ellipse>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.56 74.43L47.7 52.84 52.46 48.15 74.32 69.74 69.56 74.43z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M54.98 51.16L54.22 51.91"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M57.62 53.77L55.59 55.78"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M71.22 67.2L70.46 67.95"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68.87 64.88L66.84 66.89"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.55 48.21l5 4.89L55.42 72H51v-4.4z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M65.67 52.24L70.35 56.86"
        ></path>
      </svg>
    ),
    name: "Customize",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="76"
          height="50"
          x="7"
          y="10"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="28"
          height="7"
          x="32"
          y="69"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="1.5"
          ry="1.5"
        ></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40 60L40 69"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M52 60L52 69"
        ></path>
        <path
          fill="#c4cefe"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 24L70 24"
        ></path>
        <path
          fill="#c4cefe"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 30L70 30"
        ></path>
        <path
          fill="#c4cefe"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 36L70 36"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M24 23H36V37H24z"
        ></path>
        <rect
          width="18"
          height="30"
          x="69"
          y="50"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="3"
          ry="3"
        ></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M78.09 75.56L78.09 75.56"
        ></path>
        <rect
          width="24"
          height="34"
          x="3"
          y="46"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="3"
          ry="3"
        ></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14.69 76.66L14.69 76.66"
        ></path>
        <path fill="none" stroke="#6576ff" strokeLinecap="round" strokeLinejoin="round" d="M13.5 49.5L16.5 49.5"></path>
        <path fill="none" stroke="#6576ff" strokeLinecap="round" strokeLinejoin="round" d="M3.5 73.5L26.5 73.5"></path>
      </svg>
    ),
    name: "Devices",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path fill="#fff" d="M64.77 26.89L34.1 48 4.5 26.89 32.84 7.4a2.26 2.26 0 012.53 0z"></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M4.42 26h60.14A1.45 1.45 0 0166 27.44v33.14A1.43 1.43 0 0164.58 62H4.44A1.45 1.45 0 013 60.56V27.42A1.43 1.43 0 014.42 26z"
        ></path>
        <path
          fill="#c4cefe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M18.51 36.11l10.15-19.34 16.94 8.72L37 41.91l-1.9-1.12a1.39 1.39 0 00-1.55 0l-4.34 2.82z"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M26.52 41.7l3.33-14s18.24 4 18.7 4.35l-1.48 6.25-7.76 5.19-5.09-3.11-5 3.23z"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M32.47 32.04L42.98 34.5"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M31.64 35.5L42.16 37.96"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M30.82 38.96L41.34 41.41"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M30.17 41.73L40.68 44.18"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M29.51 44.5L35.08 45.8"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M64.64 26.43L34.1 47 4.64 26.43l28.21-19a2.29 2.29 0 012.52 0z"
        ></path>
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M65.72 61.48L35.71 41a2.31 2.31 0 00-2.71-.08L3.71 61.36"
        ></path>
        <path
          fill="#fff"
          d="M66.89 82.72l-21.2-12.54a2 2 0 01-.69-2.73l17.07-28.32a2 2 0 012.75-.69L86 51a2 2 0 01.7 2.72L69.64 82a2 2 0 01-2.75.72z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M66.89 82.72l-21.2-12.54a2 2 0 01-.69-2.73l17.07-28.32a2 2 0 012.75-.69L86 51a2 2 0 01.7 2.72L69.64 82a2 2 0 01-2.75.72z"
        ></path>
        <ellipse
          cx="72.6"
          cy="48.73"
          fill="#c4cefe"
          stroke="#c4cefe"
          strokeMiterlimit="10"
          strokeWidth="2"
          rx="1.44"
          ry="1.42"
        ></ellipse>
        <path
          fill="#9cabff"
          d="M61.92 67.69l-8.41-5a1 1 0 01-.34-1.36l.31-.52a1 1 0 011.37-.34l8.41 5a1 1 0 01.35 1.36l-.31.51a1 1 0 01-1.38.35z"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M77.65 61.3L59.79 50.73"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M76.21 63.69L58.34 53.13"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M74.76 66.09L56.9 55.52"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M73.31 68.49L55.45 57.92"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M68.01 52.34L61.48 48.47 61.48 48.47"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M67.67 77.86L49.8 67.29"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M57.43 68.5L51.51 64.99 51.51 64.99"
        ></path>
      </svg>
    ),
    name: "Emails",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="56"
          height="70"
          x="15"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.88 85H30.12A6.06 6.06 0 0124 79V21a6.06 6.06 0 016.12-6h29.54L76 30.47V79a6.06 6.06 0 01-6.12 6z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M60 16L60 31 75 31.07"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M58 50L32 50"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M46 38L32 38"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68 44L32 44"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68 56L32 56"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M58 62L32 62"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68 68L32 68"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M58 75L32 75"
        ></path>
      </svg>
    ),
    name: "File Doc",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="56"
          height="70"
          x="14.5"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.38 85H29.62a6.06 6.06 0 01-6.12-6V21a6.06 6.06 0 016.12-6h29.54L75.5 30.47V79a6.06 6.06 0 01-6.12 6z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M59.5 16L59.5 31 74.5 31.07"
        ></path>
        <path
          fill="#f5f7ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34.5 40H64.5V70H34.5z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34.5 48L63.5 48"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M45.5 70L45.5 40"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34.5 56L63.5 56"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34.5 64L63.5 64"
        ></path>
      </svg>
    ),
    name: "File Excel",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="56"
          height="70"
          x="15"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.88 85H30.12A6.06 6.06 0 0124 79V21a6.06 6.06 0 016.12-6h29.54L76 30.47V79a6.06 6.06 0 01-6.12 6z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M60 16L60 31 75 31.07"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40 45H63V64H40z"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M36 49H59V68H36z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M37 62.88L45.12 55.94 52.81 63.06 55.99 59.44 59 62.76"
        ></path>
        <circle
          cx="52.11"
          cy="54.98"
          r="2.02"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
      </svg>
    ),
    name: "File Media",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="56"
          height="70"
          x="15"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.88 85H30.12A6.06 6.06 0 0124 79V21a6.06 6.06 0 016.12-6h29.54L76 30.47V79a6.06 6.06 0 01-6.12 6z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M60 16L60 31 75 31.07"
        ></path>
        <path
          fill="#6576ff"
          d="M57.16 60.13c-.77 0-1.53 0-2.28.08l-.82.07c-.28-.31-.55-.63-.81-1a32.06 32.06 0 01-4.11-6.94 28.83 28.83 0 00.67-3.72 16.59 16.59 0 00-.49-7.29c-.29-.78-1-1.72-1.94-1.25s-1.3 2.12-1.38 3.2a11 11 0 00.12 2.63 20.88 20.88 0 00.61 2.51c.23.76.49 1.51.77 2.25-.18.59-.37 1.16-.56 1.72-.46 1.28-1 2.49-1.43 3.65l-.74 1.7c-.77 1.78-1.59 3.52-2.52 5.26a27.25 27.25 0 00-5.72 2.85 12.36 12.36 0 00-2.26 2A4.33 4.33 0 0033 70.24a1.62 1.62 0 00.59 1.39 2.36 2.36 0 002 .27c2.19-.48 3.86-2.48 5.29-4.15a46.09 46.09 0 003.27-4.41 47.26 47.26 0 016.51-1.63c1.06-.18 2.15-.34 3.26-.44a15.74 15.74 0 002.54 2.07 11.65 11.65 0 002.28 1.16 15.78 15.78 0 002.45.65 7 7 0 001.3.07c1 0 2.4-.44 2.49-1.71a1.93 1.93 0 00-.2-1C64 61 61.33 60.55 60.1 60.34a17 17 0 00-2.94-.21zm-16 4.68c-.47.75-.91 1.44-1.33 2-1 1.48-2.2 3.25-3.9 3.91a3 3 0 01-1.2.22c-.4 0-.79-.21-.77-.69a2 2 0 01.3-.89 5 5 0 01.7-1 11.3 11.3 0 012.08-1.79 24.17 24.17 0 014.4-2.33c-.08.25-.17.41-.26.57zM47 45.76a9.07 9.07 0 01-.07-2.38 6.73 6.73 0 01.22-1.12c.1-.3.29-1 .61-1.13.51-.15.67 1 .73 1.36a15.91 15.91 0 01-.36 5.87c-.06.3-.13.59-.21.88-.12-.36-.24-.73-.35-1.09a19.24 19.24 0 01-.57-2.39zm3.55 15A46.66 46.66 0 0045 62a14.87 14.87 0 001.38-2.39 29.68 29.68 0 002.42-5.92 28.69 28.69 0 003.87 6.15l.43.51c-.88.13-1.74.25-2.58.39zM63.7 63.4c-.07.41-.89.65-1.27.71a6.84 6.84 0 01-3.43-.37 10 10 0 01-2.15-1.06 12.35 12.35 0 01-1.9-1.51c.73 0 1.47-.07 2.21-.06a18.42 18.42 0 012.23.15 7.6 7.6 0 014 1.63c.24.18.31.35.28.49z"
        ></path>
      </svg>
    ),
    name: "File PDF",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="56"
          height="70"
          x="15"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.88 85H30.12A6.06 6.06 0 0124 79V21a6.06 6.06 0 016.12-6h29.54L76 30.47V79a6.06 6.06 0 01-6.12 6z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M60 16L60 31 75 31.07"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69 47L31 47"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69 53L31 53"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69 59L31 59"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69 65L31 65"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69 71L31 71"
        ></path>
      </svg>
    ),
    name: "File Word",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="56"
          height="70"
          x="14.5"
          y="5"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M69.38 85H29.62a6.06 6.06 0 01-6.12-6V21a6.06 6.06 0 016.12-6h29.54L75.5 30.47V79a6.06 6.06 0 01-6.12 6z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M59.5 16L59.5 31 74.5 31.07"
        ></path>
        <path
          fill="#6576ff"
          d="M38.29 24.84c-.27-.79-.53-1.53-.79-2.23L34.66 24c.25.66.5 1.37.75 2.11zM36.64 20.41c-.32-.79-.64-1.51-.94-2.16L33 19.87c.28.61.58 1.28.88 2zM38.94 41.52c.1.74.2 1.49.28 2.27l3.06-.45c-.09-.81-.19-1.59-.3-2.36zM42.92 52.82c0-.8-.05-1.59-.08-2.37l-3.08.18c0 .75.07 1.51.08 2.29zM39.45 46.06q.11 1.13.18 2.28l3.07-.27q-.08-1.2-.18-2.37zM39.65 29.36c-.21-.79-.43-1.54-.65-2.27l-2.91 1.13c.21.7.41 1.42.62 2.17zM31 16.08s.4.63 1 1.82l2.65-1.77C34 14.75 33.49 14 33.47 14zM38.22 37c.13.73.26 1.48.39 2.24l3-.63c-.12-.8-.26-1.58-.4-2.34zM40.76 34c-.17-.8-.34-1.57-.52-2.32l-3 .93c.17.72.34 1.45.5 2.21zM56 16.08l-1.24-1-1.24-1s-.48.75-1.19 2.13L55 17.9c.6-1.19 1-1.81 1-1.82zM44.08 52.82l3.08.1c0-.78 0-1.54.08-2.29l-3.08-.18c-.04.78-.06 1.55-.08 2.37zM44.3 48.07l3.07.27q.08-1.16.18-2.28l-3.07-.36q-.1 1.17-.18 2.37zM54 19.87l-2.7-1.62c-.3.65-.62 1.37-.94 2.16l2.79 1.49c.3-.75.6-1.43.85-2.03zM48.06 41.52L45 41c-.1.77-.2 1.56-.29 2.36l3.05.45c.1-.81.24-1.55.3-2.29zM52.34 24l-2.84-1.39c-.26.7-.52 1.44-.79 2.22l2.88 1.25c.25-.74.5-1.45.75-2.08zM45.77 36.29c-.14.76-.28 1.54-.4 2.34l3 .63c.13-.76.26-1.51.39-2.24zM40.73 63.77a.74.74 0 00-.65.81v12.61a.74.74 0 00.65.81h4.05a.75.75 0 00.66-.81V64.58a.75.75 0 00-.66-.81h-.24v2.9a1.8 1.8 0 11-3.57 0v-2.9zm1.13 9.85a1.18 1.18 0 011-1.31h.69c.55 0 1 .58 1 1.31v1.17c0 .73-.44 1.31-1 1.31h-.69a1.18 1.18 0 01-1-1.31z"
        ></path>
        <path
          fill="#6576ff"
          d="M42.76 67.57a1.39 1.39 0 001.34-1.43v-4.73a1.34 1.34 0 10-2.68 0v4.73a1.39 1.39 0 001.34 1.43z"
        ></path>
        <path
          fill="#6576ff"
          d="M43.42 54.76h3.1v2.13a8.6 8.6 0 01-.18 10.18v-3.75A1.31 1.31 0 0045.09 62h-.44v-1.48a1.89 1.89 0 10-3.78 0V62h-.45a1.31 1.31 0 00-1.26 1.34v3.77a8.55 8.55 0 01.05-10.46V54.8h3.1zM50.91 28.22L48 27.09c-.22.72-.44 1.48-.65 2.27l2.94 1c.21-.72.41-1.44.62-2.14zM49.73 32.58l-3-.93c-.18.75-.35 1.52-.52 2.32l3 .82c.18-.79.35-1.49.52-2.21z"
        ></path>
      </svg>
    ),
    name: "File ZIP",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68.14 80.86l-37.93-8.17a5.93 5.93 0 01-4.57-7l12.26-56A6 6 0 0145 5.14l28.18 6.07 12.32 18.3-10.26 46.82a6 6 0 01-7.1 4.53z"
        ></path>
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M73 12.18L69.83 26.66 85.37 30.08"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M66.26 71.15L29.05 82.46a6 6 0 01-7.46-4L4.76 23.15a6 6 0 014-7.47l27.64-8.4 19.76 10.11 14.08 46.29a6 6 0 01-3.98 7.47z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M36.7 8.22L41.05 22.53 56.33 17.96"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M68 85H29a6 6 0 01-6-6V21a6 6 0 016-6h29l16 15.47V79a6 6 0 01-6 6z"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M58 16L58 31 74 31.07"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M45 41L61 41"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 48L61 48"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 55L61 55"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 63L61 63"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 69L51 69"
        ></path>
        <text
          fill="#6576ff"
          fontFamily="Nunito-Black, Nunito Black"
          fontSize="9.31"
          transform="matrix(.99 0 0 1 34.54 43.18)"
        >
          $
        </text>
      </svg>
    ),
    name: "Invoice",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="70"
          height="60"
          x="5"
          y="10"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="70"
          height="60"
          x="15"
          y="20"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path
          fill="#e3e7fe"
          fillRule="evenodd"
          d="M47.4 52.58s-10.17 10.18-15.77 3.58c-4.88-5.76-5.13-11.09-1.41-14.81s11.53-3.87 15.92 1.19 10 10.79 12.49 12.35 11.83 2.75 13.62-5.36-5.13-9.3-7.59-9.67c-.69-.1-2.27 1-4.39 2.29-5.34 3.27-12.87 10.43-12.87 10.43z"
        ></path>
        <path
          fill="#6576ff"
          fillRule="evenodd"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M44.66 41.42a11 11 0 00-4.81-2.78 10.12 10.12 0 100 19.72 11 11 0 004.81-2.78q1.58-1.45 3.1-2.94l-.2-.19c-1 1-2.05 2-3.08 2.93a10.65 10.65 0 01-4.7 2.71 9.84 9.84 0 110-19.18 10.65 10.65 0 014.7 2.71c4.52 4.22 8.85 8.64 13.38 12.86A9.48 9.48 0 0062 56.89a8.61 8.61 0 100-16.78 9.48 9.48 0 00-4.11 2.41c-1 .95-2 1.91-3 2.88l.2.19 3-2.87a9.3 9.3 0 014-2.34 8.34 8.34 0 110 16.24 9.3 9.3 0 01-4-2.34c-4.52-4.22-8.85-8.65-13.38-12.86z"
        ></path>
      </svg>
    ),
    name: "Loop",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40 8h12"
        ></path>
        <rect
          width="50"
          height="72"
          x="23.5"
          y="3"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="2"
        ></rect>
        <path fill="#e3e7fe" d="M26.5 13h44v58a2 2 0 01-2 2h-40a2 2 0 01-2-2V13z"></path>
        <rect
          width="48"
          height="14"
          x="34.5"
          y="65"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="2"
        ></rect>
        <circle
          cx="23.5"
          cy="71"
          r="16"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <circle cx="23.5" cy="71" r="12" fill="#e3e7fe"></circle>
        <path
          fill="#6576ff"
          d="M18.44 70h10.12A1.45 1.45 0 0130 71.44v5.09A1.45 1.45 0 0128.56 78H18.44A1.45 1.45 0 0117 76.53v-5.09A1.45 1.45 0 0118.44 70z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M20 70v-2.53a3.5 3.5 0 017 0V70"
        ></path>
        <circle cx="23.5" cy="73.23" r="1.5" fill="#fff"></circle>
        <path fill="#fff" d="M22.5 74.23h2v1.24a.76.76 0 01-.76.76h-.47a.76.76 0 01-.76-.76v-1.24h-.01z"></path>
        <g fill="none" stroke="#6576ff" strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5">
          <path d="M48.5 69.5v5M50.5 70.79l-4 2.52M50.5 73.31l-4-2.52M57.17 69.5v5M59.17 70.79l-4 2.52M59.17 73.31l-4-2.52M65.83 69.5v5M67.83 70.79l-4 2.52M67.83 73.31l-4-2.52M74.5 69.5v5M76.5 70.79l-4 2.52M76.5 73.31l-4-2.52"></path>
        </g>
        <path fill="#fff" d="M30.5 36h34v5h-34zM30.5 43.75h34v5h-34z"></path>
        <path fill="#6576ff" d="M30.5 50.5h34v7h-34z"></path>
        <path
          fill="none"
          stroke="#fff"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M49.5 52.5l-2.75 3-1.25-1.36"
        ></path>
        <circle cx="48.5" cy="23.5" r="7.5" fill="#c4cefe"></circle>
      </svg>
    ),
    name: "Password Secure",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="60"
          height="56"
          x="5"
          y="7"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="60"
          height="56"
          x="25"
          y="27"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="60"
          height="56"
          x="15"
          y="17"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M15 29L75 29"></path>
        <circle cx="53" cy="23" r="2" fill="#c4cefe"></circle>
        <circle cx="60" cy="23" r="2" fill="#c4cefe"></circle>
        <circle cx="67" cy="23" r="2" fill="#c4cefe"></circle>
        <rect
          width="20"
          height="20"
          x="22"
          y="39"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="2"
          ry="2"
        ></rect>
        <circle
          cx="32"
          cy="45.81"
          r="2"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M29 54.31a3 3 0 016 0"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M49 40L69 40"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M49 51L69 51"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M49 57L59 57"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M64 57L66 57"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M49 46L59 46"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M64 46L66 46"
        ></path>
      </svg>
    ),
    name: "Profile",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M26 70.78V24.5a7 7 0 017-7h36a9 9 0 019 9v49a7 7 0 01-7 7H16.55s9.17-3.61 9.45-11.72z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M7 30.5h19v43.4a8.6 8.6 0 01-8.6 8.6h-3.8A8.6 8.6 0 015 73.9V32.5a2 2 0 012-2z"
        ></path>
        <circle
          cx="71.5"
          cy="21"
          r="13.5"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <rect width="16" height="8" x="34" y="33.5" fill="#c4cefe" rx="1" ry="1"></rect>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 46.5L67 46.5"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 53.5L67 53.5"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 59.5L67 59.5"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 64.5L67 64.5"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M35 71.5L51 71.5"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M75.24 23.79a5.2 5.2 0 01-6.42 2.57 5.78 5.78 0 01-3.26-7.25 5.25 5.25 0 016.8-3.47 5.35 5.35 0 012 1.34l2.75 2.75"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M77.75 16.61L77.75 20.61 73.75 20.61"
        ></path>
      </svg>
    ),
    name: "Recheck",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40.74 5.16l38.67 9.23a6 6 0 014.43 7.22L70.08 80a6 6 0 01-7.17 4.46l-38.68-9.24A6 6 0 0119.81 68L33.56 9.62a6 6 0 017.18-4.46z"
        ></path>
        <path
          fill="#eff1ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M50.59 6.5l-39.41 5.23a6 6 0 00-5.13 6.73L13.85 78a6 6 0 006.69 5.16l39.4-5.23a6 6 0 005.14-6.73l-7.8-59.49a6 6 0 00-6.69-5.21z"
        ></path>
        <rect
          width="54"
          height="70"
          x="15"
          y="15"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="6"
          ry="6"
        ></rect>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 77L58 77"
        ></path>
        <circle cx="38" cy="77" r="0.5" fill="#c4cefe" stroke="#c4cefe" strokeMiterlimit="10"></circle>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 72L58 72"
        ></path>
        <circle cx="38" cy="72" r="0.5" fill="#c4cefe" stroke="#c4cefe" strokeMiterlimit="10"></circle>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 66L58 66"
        ></path>
        <circle cx="38" cy="66" r="0.5" fill="#c4cefe" stroke="#c4cefe" strokeMiterlimit="10"></circle>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M46 40l-15-.3v.3a15 15 0 1015-15h-.36z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 22a14 14 0 00-14 14h14V22"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M33.47 30.07L28.87 23 23 23"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M25 56L35 56 40.14 49"
        ></path>
      </svg>
    ),
    name: "Reports",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="70"
          height="50"
          x="5"
          y="8"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="70"
          height="50"
          x="15"
          y="16"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="30"
          height="8"
          x="35"
          y="74"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="1"
          ry="1"
        ></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M42 66L42 74"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M58 66L58 74"
        ></path>
        <rect width="50" height="34" x="25" y="24" fill="#eff1ff" rx="3" ry="3"></rect>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M63 51L63 48"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M63 41L63 31"
        ></path>
        <circle
          cx="63"
          cy="45"
          r="3"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M54 31L54 34"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M54 41L54 51"
        ></path>
        <circle
          cx="54"
          cy="37"
          r="3"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M44 31L44 38"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M44 44L44 51"
        ></path>
        <circle
          cx="44"
          cy="41"
          r="3"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34 31L34 34"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M34 41L34 51"
        ></path>
        <circle
          cx="34"
          cy="37"
          r="3"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
      </svg>
    ),
    name: "Settings",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="70"
          height="51.71"
          x="5"
          y="9"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M15 63.7V25.91a7 7 0 017-7h56a7 7 0 017 7V63.7a7 7 0 01-7 7H31L15 81z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M63 51.72v4.39a2.94 2.94 0 01-3 2.94h-.28A29.49 29.49 0 0147 54.54a29.26 29.26 0 01-13.42-21.47 2.93 2.93 0 012.68-3.17H41a3 3 0 013 3.27 3.75 3.75 0 00.63 2.65 2.9 2.9 0 01-.27 3.8l-1.88 1.86a23.51 23.51 0 008.87 8.78l1.88-1.86a3 3 0 013.15-.65 19.64 19.64 0 004.13 1A2.93 2.93 0 0163 51.72z"
        ></path>
        <text
          fill="#9cabff"
          fontFamily="Nunito-Bold, Nunito"
          fontSize="14.07"
          fontWeight="700"
          letterSpacing="-.05em"
          transform="matrix(1.01 0 0 1 50.23 41.17)"
        >
          24
        </text>
      </svg>
    ),
    name: "Support",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeMiterlimit="10"
          strokeWidth="2"
          d="M62.92 87H10.08A4.08 4.08 0 016 82.92V7.08A4.08 4.08 0 0110.08 3h37.74L67 21.23v61.69A4.08 4.08 0 0162.92 87zM47 3v13.92A4 4 0 0050.88 21H66"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 14L28 14"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 19L36 19"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 37L28 37"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 42L45 42"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 49L40 49"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 56L32 56"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 63L38 63"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M14 70L30 70"
        ></path>
        <path
          fill="#6576ff"
          d="M67 40h9a9.59 9.59 0 008 8.32s.89 19-17 27.68c-17.88-8.72-17-27.68-17-27.68A9.59 9.59 0 0058 40z"
        ></path>
        <path fill="#fff" d="M67 72.51c-14.88-7.16-14.14-22.69-14.14-22.69A7.94 7.94 0 0059.52 43H67z"></path>
        <path fill="#fff" d="M62 61.77a11.05 11.05 0 0112 0V65H62z"></path>
        <circle cx="68.5" cy="54.5" r="3.5" fill="#fff"></circle>
        <path
          fill="#6576ff"
          d="M68.47 58.13a4 4 0 114-4 4 4 0 01-4 4zm0-6.92a3 3 0 103 3 3 3 0 00-3-3zm0 0M74.5 66h-13a.51.51 0 01-.5-.5v-3.4a1.46 1.46 0 01.69-1.25 12.32 12.32 0 0112.66 0A1.46 1.46 0 0175 62.1v3.41a.49.49 0 01-.5.49zM62 65h12v-2.91a.47.47 0 00-.22-.41 11.28 11.28 0 00-11.56 0 .47.47 0 00-.22.41zm.53 0"
        ></path>
      </svg>
    ),
    name: "Support Account",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeMiterlimit="10"
          strokeWidth="2"
          d="M63.42 87H10.58a4.08 4.08 0 01-4.08-4.08V7.08A4.08 4.08 0 0110.58 3h37.74L67.5 21.23v61.69A4.08 4.08 0 0163.42 87zM47.5 3v13.92A4 4 0 0051.38 21H66.5"
        ></path>
        <circle cx="63.5" cy="67" r="20" fill="#6576ff"></circle>
        <path
          fill="#fff"
          d="M61.87 71a6.83 6.83 0 01.39-2.55 6.71 6.71 0 011.51-2.09 11.82 11.82 0 001.44-1.61 2.92 2.92 0 00.47-1.59 2.47 2.47 0 00-.55-1.72 2 2 0 00-1.58-.6 2.22 2.22 0 00-1.61.59 2 2 0 00-.61 1.57H58.5a4.39 4.39 0 011.4-3.37 5.27 5.27 0 013.65-1.24 5.09 5.09 0 013.64 1.23 4.48 4.48 0 011.31 3.43 5.69 5.69 0 01-1.77 3.86l-1.43 1.46a4.08 4.08 0 00-.79 2.63zm-.3 3.17A1.6 1.6 0 0162 73a1.69 1.69 0 013 1.17 1.65 1.65 0 01-.44 1.17 1.67 1.67 0 01-1.26.46 1.62 1.62 0 01-1.3-.46 1.65 1.65 0 01-.43-1.17z"
        ></path>
        <circle cx="17" cy="21.5" r="4.5" fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2"></circle>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M28.5 20L36.5 20"></path>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M28.5 24L43.5 24"></path>
        <circle cx="17" cy="39.5" r="4.5" fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2"></circle>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M28.5 37L36.5 37"></path>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M28.5 42L43.5 42"></path>
        <circle cx="17" cy="56.5" r="4.5" fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2"></circle>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M28.5 54L36.5 54"></path>
        <path fill="none" stroke="#6576ff" strokeMiterlimit="10" strokeWidth="2" d="M28.5 59L43.5 59"></path>
      </svg>
    ),
    name: "Support General",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M29 74H11a7 7 0 01-7-7V17a7 7 0 017-7h50a7 7 0 017 7v13"
        ></path>
        <path fill="#e3e7fe" d="M11 11h50a6 6 0 016 6v4H5v-4a6 6 0 016-6z"></path>
        <circle cx="11" cy="16" r="2" fill="#6576ff"></circle>
        <circle cx="17" cy="16" r="2" fill="#6576ff"></circle>
        <circle cx="23" cy="16" r="2" fill="#6576ff"></circle>
        <rect width="19" height="19" x="11" y="27" fill="#c4cefe" rx="1" ry="1"></rect>
        <path
          fill="#c4cefe"
          d="M33.8 53.79c.33-.43.16-.79-.39-.79H12a1 1 0 00-1 1v10a1 1 0 001 1h19a1 1 0 001-1v-4.81a10.81 10.81 0 01.23-2z"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M36 29L60 29"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M36 34L55 34"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M36 39L50 39"
        ></path>
        <path
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M36 44L46 44"
        ></path>
        <path fill="#6576ff" d="M4 21H68V23H4z"></path>
        <rect
          width="38"
          height="24"
          x="36"
          y="56"
          fill="#fff"
          stroke="#e3e7fe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="5"
          ry="5"
        ></rect>
        <rect width="12" height="9" x="41" y="60" fill="#c4cefe" rx="1" ry="1"></rect>
        <path
          fill="#6576ff"
          d="M84.74 53.51L66.48 35.25a4.31 4.31 0 00-6.09 0l-9.13 9.13a4.31 4.31 0 000 6.09l18.26 18.26a4.31 4.31 0 006.09 0l9.13-9.13a4.31 4.31 0 000-6.09zm-15-5.89L67 50.3a2.15 2.15 0 01-3 0l-4.76-4.76a2.16 2.16 0 010-3l2.67-2.67a2.16 2.16 0 013 0l4.76 4.76a2.15 2.15 0 01.05 2.99z"
        ></path>
      </svg>
    ),
    name: "Support Payment",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="64"
          height="63.37"
          x="3"
          y="12.5"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path fill="#e3e7fe" d="M10 13.49h50a6 6 0 016 6v3.9H4v-3.9a6 6 0 016-6z"></path>
        <path fill="#6576ff" d="M3 23.39H67V25.37H3z"></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeMiterlimit="10"
          strokeWidth="2"
          d="M65.37 31.31h11.44A12.24 12.24 0 0087 42s1.12 24.31-21.63 35.5C42.62 66.31 43.75 42 43.75 42a12.23 12.23 0 0010.18-10.69z"
        ></path>
        <path fill="#e3e7fe" d="M66 72.62c19-9.05 18.1-28.71 18.1-28.71s-7.47-.94-8.52-8.64H66z"></path>
        <path fill="#010863" d="M56 46.16L55 46.16 55 42.2 59 42.2 59 43.2 56 43.2 56 46.16z"></path>
        <path fill="#010863" d="M59 65.97L55 65.97 55 62.01 56 62.01 56 64.98 59 64.98 59 65.97z"></path>
        <path fill="#010863" d="M78 65.97L74 65.97 74 64.98 77 64.98 77 62.01 78 62.01 78 65.97z"></path>
        <path fill="#010863" d="M78 46.16L77 46.16 77 43.2 74 43.2 74 42.2 78 42.2 78 46.16z"></path>
        <path
          fill="#6576ff"
          d="M70 51.12h-8v-2.26a3.74 3.74 0 013.17-3.57c2.56-.46 4.83 1.28 4.83 3.49zm-7-1h6v-1.56a2.78 2.78 0 00-2-2.63 3 3 0 00-4 2.64z"
        ></path>
        <path fill="#e5effe" d="M58 57.28v-7.15h16v7.37c0 4.62-4.65 8.26-9.86 7.17A7.63 7.63 0 0158 57.28z"></path>
        <path fill="#6576ff" d="M59 51.12v6.7A7 7 0 0073 58v-6.88z"></path>
        <ellipse cx="66" cy="55.08" fill="#fff" rx="2" ry="1.98"></ellipse>
        <path fill="#fff" d="M68.91 62.01L63.84 62.01 65.18 56.07 67.57 56.07 68.91 62.01z"></path>
        <path
          fill="#6576ff"
          d="M72 51.12H60v-2.46a5.41 5.41 0 014.06-5.14c4.13-1.14 7.94 1.54 7.94 5zm-11-1h10v-1.63A4.69 4.69 0 0067.08 44c-3.23-.6-6.08 1.58-6.08 4.33z"
        ></path>
        <rect
          width="22"
          height="5.94"
          x="13"
          y="32.3"
          fill="none"
          stroke="#6576ff"
          strokeMiterlimit="10"
          strokeWidth="2"
          rx="1"
          ry="1"
        ></rect>
        <rect
          width="22"
          height="5.94"
          x="12"
          y="45.17"
          fill="none"
          stroke="#6576ff"
          strokeMiterlimit="10"
          strokeWidth="2"
          rx="1"
          ry="1"
        ></rect>
        <rect
          width="12"
          height="5.94"
          x="12"
          y="57.06"
          fill="none"
          stroke="#6576ff"
          strokeMiterlimit="10"
          strokeWidth="2"
          rx="1"
          ry="1"
        ></rect>
      </svg>
    ),
    name: "Support Secure",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <rect
          width="70"
          height="50"
          x="3"
          y="10"
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <rect
          width="70"
          height="50"
          x="13"
          y="24"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="7"
          ry="7"
        ></rect>
        <path
          fill="none"
          stroke="#9cabff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M20 33L31 33"
        ></path>
        <path
          fill="none"
          stroke="#9cabff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M75 33L77 33"
        ></path>
        <path
          fill="none"
          stroke="#9cabff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M66 33L67 33"
        ></path>
        <path
          fill="none"
          stroke="#9cabff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M70 33L72 33"
        ></path>
        <rect width="56" height="7" x="19" y="40" fill="#eff1ff" rx="2" ry="2"></rect>
        <rect width="24" height="8" x="20" y="51" fill="#eff1ff" rx="2" ry="2"></rect>
        <rect width="7" height="8" x="48" y="51" fill="#eff1ff" rx="2" ry="2"></rect>
        <ellipse
          cx="69"
          cy="61.98"
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="18"
          ry="18.02"
        ></ellipse>
        <circle cx="69" cy="62" r="7" fill="#e3e7fe"></circle>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M77 56L77 60 73 60"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M62.26 59.17a6.81 6.81 0 0111.25-2.55l3.49 3.3"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M61 67L61 63 65 63"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M61.43 64l3.51 3.31a6.83 6.83 0 0011.26-2.52"
        ></path>
      </svg>
    ),
    name: "sync",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <path fill="#e3e7fe" d="M81.8 22.23a2.89 2.89 0 112.89-2.89 2.89 2.89 0 01-2.89 2.89"></path>
        <circle
          cx="72.42"
          cy="73.31"
          r="2.89"
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></circle>
        <path
          fill="#e3e7fe"
          d="M15 78.12a1.93 1.93 0 111.93-1.92A1.93 1.93 0 0115 78.12M14 51.35a1.93 1.93 0 111.93-1.93A1.94 1.94 0 0114 51.35"
        ></path>
        <path fill="#e3e7fe" d="M6.01 13.19H11.98V15.18H6.01z"></path>
        <path fill="#e3e7fe" d="M8 11.2H9.99V17.169999999999998H8z"></path>
        <path fill="#e3e7fe" d="M74.3 34.88L68.3 26.12 66.35 15.69 47.16 34.88 74.3 34.88z"></path>
        <path fill="#e3e7fe" d="M42.54 6.45L33.78 12.44 23.34 14.39 42.54 33.59 42.54 6.45z"></path>
        <path fill="#e3e7fe" d="M14.16 37.73L20.15 46.49 22.11 56.92 41.3 37.73 14.16 37.73z"></path>
        <path fill="#e3e7fe" d="M23.34 58.64L33.78 60.59 42.54 66.59 42.54 39.44 23.34 58.64z"></path>
        <path fill="#e3e7fe" d="M22.11 15.69L20.15 26.12 14.16 34.88 41.3 34.88 22.11 15.69z"></path>
        <path fill="#e3e7fe" d="M74.3 37.87L47.16 37.87 66.35 57.06 68.3 46.62 74.3 37.87z"></path>
        <path fill="#e3e7fe" d="M45.52 66.59L54.28 60.59 64.72 58.64 45.52 39.44 45.52 66.59z"></path>
        <path fill="#e3e7fe" d="M64.72 14.39L54.28 12.44 45.52 6.45 45.52 33.59 64.72 14.39z"></path>
        <path
          fill="#e3e7fe"
          d="M53.84 62.75L44.03 69.48 34.22 62.75 30.6 62.75 30.6 85.64 34.22 85.64 44.03 78.91 53.84 85.64 57.47 85.64 57.47 62.75 53.84 62.75z"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M61.84 36.52A17.62 17.62 0 1144.23 18.9a17.61 17.61 0 0117.61 17.62"
        ></path>
        <path
          fill="#6576ff"
          d="M50.71 30.06v9a1.44 1.44 0 01-.77 1.26l-4.67 2.55a1.62 1.62 0 01-1.54 0l-4.67-2.55a1.44 1.44 0 01-.77-1.26V34a1.44 1.44 0 01.77-1.26l4.67-2.55a1.6 1.6 0 011.4-.07v1.52a1.26 1.26 0 00-1.19 0l-3.63 2a1.1 1.1 0 00-.59 1v4a1.1 1.1 0 00.59 1l3.63 2a1.26 1.26 0 001.19 0l3.62-2a1.11 1.11 0 00.6-1V29.3z"
        ></path>
        <path
          fill="#6576ff"
          d="M47.92 28.54l-1.4-.76v9.55a.45.45 0 01-.25.41l-1.52.83a.5.5 0 01-.5 0l-1.52-.83a.45.45 0 01-.25-.41v-1.66a.45.45 0 01.25-.41l1.52-.83a.5.5 0 01.5 0l.38.2v-1.52l-.2-.11a.93.93 0 00-.86 0l-2.56 1.4a.79.79 0 00-.43.7v2.8a.79.79 0 00.43.7l2.56 1.4a.93.93 0 00.86 0l2.56-1.4a.81.81 0 00.43-.7v-9.36z"
        ></path>
        <path
          fill="#6576ff"
          d="M52.11 30.83v8.79a1.77 1.77 0 01-1 1.55l-5.72 3.12a2 2 0 01-1.88 0l-5.72-3.12a1.76 1.76 0 01-.94-1.55v-6.24a1.76 1.76 0 01.94-1.55l5.72-3.12a2 2 0 011.88 0l1.08.59v-1.52l-.9-.5a2.4 2.4 0 00-2.24 0L36.62 31a2.07 2.07 0 00-1.12 1.83v7.38A2.07 2.07 0 0036.62 42l6.76 3.7a2.4 2.4 0 002.24 0l6.76-3.7a2.07 2.07 0 001.12-1.83V31.6z"
        ></path>
      </svg>
    ),
    name: "Plan 1",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <circle cx="24.83" cy="6.55" r="3.06" fill="#e3e7fe"></circle>
        <circle
          cx="83.97"
          cy="28.73"
          r="3.03"
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></circle>
        <circle cx="83.58" cy="67.84" r="1.84" fill="#e3e7fe"></circle>
        <circle cx="12.04" cy="84.67" r="1.84" fill="#e3e7fe"></circle>
        <path fill="#e3e7fe" d="M3 47.84H9.120000000000001V49.59H3z"></path>
        <path fill="#e3e7fe" d="M3 47.84H9.120000000000001V49.59H3z" transform="rotate(90 6.06 48.72)"></path>
        <circle cx="45.76" cy="64.92" r="19.82" fill="#fff"></circle>
        <circle
          cx="45.76"
          cy="64.92"
          r="18.24"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
        ></circle>
        <path
          fill="#f3f5ff"
          stroke="#6576ff"
          strokeLinecap="round"
          d="M37.5 22.5v21.39a23.66 23.66 0 0117 .1V22.5z"
        ></path>
        <path
          fill="#e3e7fe"
          stroke="#6576ff"
          strokeLinecap="round"
          d="M20.5 22.5v23l6.5 6.21a23.73 23.73 0 0110.5-7.82V22.5zM54.5 22.5V44a23.68 23.68 0 0110.32 7.86l6.68-6.37v-23z"
        ></path>
        <rect
          width="68"
          height="8"
          x="12"
          y="14"
          fill="none"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          rx="2"
          ry="2"
        ></rect>
        <path
          fill="#6576ff"
          d="M52 58v9.62a1.56 1.56 0 01-.74 1.38l-4.72 2.73a1.55 1.55 0 01-1.56 0L40.25 69a1.55 1.55 0 01-.77-1.34v-5.47a1.55 1.55 0 01.77-1.35L45 58.11a1.56 1.56 0 011.39-.11v1.62a1.22 1.22 0 00-1.2 0l-3.67 2.12a1.2 1.2 0 00-.6 1V67a1.23 1.23 0 00.6 1l3.67 2.11a1.17 1.17 0 001.2 0L50 68.08a1.21 1.21 0 00.61-1v-9.87z"
        ></path>
        <path
          fill="#6576ff"
          d="M49.22 56.4l-1.42-.82V65.8a.52.52 0 01-.25.45l-1.55.88a.52.52 0 01-.51 0L44 66.25a.52.52 0 01-.26-.45V64a.51.51 0 01.26-.44l1.53-.89a.52.52 0 01.51 0l.38.22v-1.6l-.2-.12a.88.88 0 00-.86 0l-2.6 1.5a.85.85 0 00-.43.75v3a.85.85 0 00.43.75l2.6 1.5a.83.83 0 00.86 0l2.59-1.5a.86.86 0 00.44-.75V58z"
        ></path>
        <path
          fill="#6576ff"
          d="M53.45 58.85v9.41a1.9 1.9 0 01-1 1.65l-5.79 3.34a1.92 1.92 0 01-1.91 0L39 69.91a1.9 1.9 0 01-1-1.65v-6.69a1.9 1.9 0 011-1.65l5.78-3.34a1.92 1.92 0 011.91 0l1.09.63v-1.63l-.91-.53a2.28 2.28 0 00-2.26 0l-6.85 4a2.25 2.25 0 00-1.13 2v7.91a2.27 2.27 0 001.13 2l6.85 4a2.28 2.28 0 002.26 0l6.84-4a2.27 2.27 0 001.13-2v-9.28z"
        ></path>
      </svg>
    ),
    name: "Plan 2",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 90 90">
        <circle
          cx="82.03"
          cy="26.32"
          r="2.97"
          fill="none"
          stroke="#c4cefe"
          strokeLinecap="round"
          strokeLinejoin="round"
        ></circle>
        <circle cx="79.41" cy="68.42" r="2.87" fill="#e3e7fe"></circle>
        <circle cx="14.98" cy="82.08" r="1.72" fill="#e3e7fe"></circle>
        <path fill="#e3e7fe" d="M5 34.77H11.879999999999999V37.06H5z"></path>
        <path fill="#e3e7fe" d="M5 34.77H11.879999999999999V37.06H5z" transform="rotate(90 8.445 35.915)"></path>
        <path
          fill="#6576ff"
          d="M39.21 4.22L42.89 5.55 45.92 3.33 45.97 7.18 49.17 9.48 45.52 10.53 44.47 14.18 42.16 10.98 38.31 10.94 40.54 7.91 39.21 4.22z"
        ></path>
        <path
          fill="#b3c2ff"
          d="M29.41 10.28L31.41 11 33.06 9.79 33.08 11.88 34.82 13.14 32.84 13.71 32.27 15.69 31.02 13.95 28.92 13.93 30.13 12.28 29.41 10.28z"
        ></path>
        <path
          fill="#b3c2ff"
          d="M51.56 9.67L53.89 10.52 55.81 9.11 55.84 11.54 57.87 13 55.56 13.67 54.89 15.98 53.44 13.95 51 13.92 52.41 12 51.56 9.67z"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M49.89 64.32a.77.77 0 00-1-.78 21.57 21.57 0 01-4.44.67 21.73 21.73 0 01-4.45-.67.77.77 0 00-1 .78v3a1 1 0 001 1h8.81a1 1 0 001-1zm.46-.17M58.43 20.88H30.92A2.08 2.08 0 0028.84 23v17.1a21.46 21.46 0 004.51 13.75 14.46 14.46 0 0022.65 0 21.46 21.46 0 004.51-13.75V23a2.08 2.08 0 00-2.08-2.12zM24.15 44.87c-2.71-1-4-3.51-4-7.59V27.8h4v-3.95h-5.84a2.07 2.07 0 00-2.07 2.07v11.36c0 6.93 3.16 11.15 9 12.14a26.26 26.26 0 01-1.06-4.55z"
        ></path>
        <path
          fill="#6576ff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          d="M28.5 72.92v2.16c0 .23.94.42 2.1.42h27.8c1.16 0 2.1-.19 2.1-.42v-2.16c0-.23-.94-.42-2.1-.42H30.6c-1.16 0-2.1.19-2.1.42zm2.85.12M28.5 78.92v2.16c0 .23.94.42 2.1.42h27.8c1.16 0 2.1-.19 2.1-.42v-2.16c0-.23-.94-.42-2.1-.42H30.6c-1.16 0-2.1.19-2.1.42zm2.85.12"
        ></path>
        <path
          fill="#fff"
          stroke="#6576ff"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M71.64 23.85h-5.93v3.95h4v8.84c0 3.89-1.24 6.22-4 7.23a24 24 0 01-1.06 4.33c5.81-.94 9-5 9-11.56V25.82a2 2 0 00-2-2z"
        ></path>
        <path
          fill="#6576ff"
          d="M50.56 33.64v9a1.45 1.45 0 01-.72 1.26l-4.44 2.58a1.45 1.45 0 01-1.45 0l-4.43-2.56a1.44 1.44 0 01-.73-1.26v-5.12a1.44 1.44 0 01.73-1.26L44 33.72a1.43 1.43 0 011.32-.07v1.52a1.14 1.14 0 00-1.13 0l-3.44 2a1.11 1.11 0 00-.56 1v4a1.12 1.12 0 00.56 1l3.44 2a1.14 1.14 0 001.13 0l3.44-2a1.12 1.12 0 00.56-1v-9.2z"
        ></path>
        <path
          fill="#6576ff"
          d="M47.92 32.11l-1.33-.76v9.53a.49.49 0 01-.24.42l-1.43.83a.51.51 0 01-.48 0L43 41.35a.49.49 0 01-.24-.42v-1.66a.49.49 0 01.24-.42l1.44-.85a.46.46 0 01.48 0l.35.21V36.7l-.19-.11a.79.79 0 00-.81 0L41.84 38a.82.82 0 00-.41.7v2.8a.81.81 0 00.41.7l2.43 1.41a.83.83 0 00.81 0l2.43-1.41a.81.81 0 00.41-.7v-7.86z"
        ></path>
        <path
          fill="#6576ff"
          d="M51.89 34.41v8.82a1.78 1.78 0 01-.9 1.55l-5.42 3.13a1.79 1.79 0 01-1.79 0l-5.42-3.13a1.78 1.78 0 01-.9-1.55V37a1.78 1.78 0 01.9-1.55l5.42-3.13a1.79 1.79 0 011.79 0l1 .59v-1.56l-.86-.5a2.1 2.1 0 00-2.11 0l-6.4 3.71a2.11 2.11 0 00-1.06 1.83v7.42a2.11 2.11 0 001.06 1.83l6.42 3.7a2.1 2.1 0 002.11 0l6.42-3.7a2.11 2.11 0 001.06-1.83v-8.62z"
        ></path>
      </svg>
    ),
    name: "Plan 3",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 114 113.9">
        <path
          fill="#c4cefe"
          d="M83.84 108.24l-48.31-7.86a3.55 3.55 0 01-3.1-4l12.2-69.48a3.66 3.66 0 014.29-2.8l48.32 7.8a3.56 3.56 0 013.09 4l-12.2 69.52a3.66 3.66 0 01-4.29 2.82z"
        ></path>
        <path
          fill="#c4cefe"
          d="M29.73 103.29L74.66 96a3.41 3.41 0 002.84-3.94L65.4 22.95a3.5 3.5 0 00-4-2.82l-44.96 7.28a3.41 3.41 0 00-2.84 3.94l12.1 69.11a3.52 3.52 0 004.03 2.83z"
        ></path>
        <rect width="66" height="88" x="22" y="17.9" fill="#6576ff" rx="3" ry="3"></rect>
        <rect width="48" height="10" x="31" y="85.9" fill="#fff" rx="1.5" ry="1.5"></rect>
        <rect width="48" height="5" x="31" y="27.9" fill="#e3e7fe" rx="1" ry="1"></rect>
        <rect width="23" height="3" x="31" y="37.9" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="20" height="3" x="59" y="37.9" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="23" height="3" x="31" y="45.9" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="20" height="3" x="59" y="45.9" fill="#c4cefe" rx="1" ry="1"></rect>
        <rect width="48" height="3" x="31" y="52.9" fill="#e3e7fe" rx="1" ry="1"></rect>
        <rect width="23" height="3" x="31" y="60.9" fill="#c4cefe" rx="1" ry="1"></rect>
        <path
          fill="#9cabff"
          d="M94.5 113.9a.5.5 0 01-.5-.5v-1.5h-1.5a.5.5 0 010-1H94v-1.5a.5.5 0 011 0v1.5h1.5a.5.5 0 010 1H95v1.5a.5.5 0 01-.5.5zM12.5 82.9a.5.5 0 01-.5-.5v-1.5h-1.5a.5.5 0 010-1H12v-1.5a.5.5 0 011 0v1.5h1.5a.5.5 0 010 1H13v1.5a.5.5 0 01-.5.5zM3 10.9a3 3 0 113-3 3 3 0 01-3 3zm0-5a2 2 0 102 2 2 2 0 00-2-2zM109.5 68.9a4.5 4.5 0 114.5-4.5 4.51 4.51 0 01-4.5 4.5zm0-8a3.5 3.5 0 103.5 3.5 3.5 3.5 0 00-3.5-3.5z"
        ></path>
        <path
          fill="#2ec98a"
          d="M103.66 4.95A5.66 5.66 0 0099.57.9a47.45 47.45 0 00-18.09 0 5.66 5.66 0 00-4.08 4.06 47.51 47.51 0 000 18.1 5.67 5.67 0 004.08 4.07 47.57 47.57 0 009 .87 47.78 47.78 0 009.06-.87 5.66 5.66 0 004.08-4.09 47.45 47.45 0 00.04-18.09z"
        ></path>
        <path
          fill="#fff"
          d="M96.66 10.71l-1.35 1.47c-1.9 2.06-3.88 4.21-5.77 6.3a1.29 1.29 0 01-1 .42 1.27 1.27 0 01-1-.42c-1.09-1.2-2.19-2.39-3.28-3.56a1.29 1.29 0 011.88-1.76c.78.84 1.57 1.68 2.35 2.54 1.6-1.76 3.25-3.55 4.83-5.27l1.35-1.46a1.29 1.29 0 011.9 1.74z"
        ></path>
      </svg>
    ),
    name: "Doc Checked",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 118">
        <path
          fill="#f6faff"
          d="M8.916 93.745C-.318 78.153-2.164 57.569 2.382 39.578 7.155 20.69 19.045 8.451 35.162 3.32 46.609-.324 58.716-.669 70.456.845 84.683 2.68 99.57 7.694 108.892 20.408c10.03 13.679 12.071 34.71 10.747 52.054-1.173 15.359-7.441 27.489-19.231 34.494-10.689 6.351-22.92 8.733-34.715 10.331-16.181 2.192-34.195-.336-47.6-12.281a47.243 47.243 0 01-9.177-11.261z"
        ></path>
        <rect width="84" height="50" x="18" y="32" fill="#fff" rx="4" ry="4"></rect>
        <rect width="20" height="12" x="26" y="44" fill="#e5effe" rx="1" ry="1"></rect>
        <rect width="20" height="12" x="50" y="44" fill="#e5effe" rx="1" ry="1"></rect>
        <rect width="20" height="12" x="74" y="44" fill="#e5effe" rx="1" ry="1"></rect>
        <rect width="20" height="12" x="38" y="60" fill="#e5effe" rx="1" ry="1"></rect>
        <rect width="20" height="12" x="62" y="60" fill="#e5effe" rx="1" ry="1"></rect>
        <path
          fill="#798bff"
          d="M98 31H22a5.006 5.006 0 00-5 5v42a5.006 5.006 0 005 5h30v8h-7a2 2 0 00-2 2v4a2 2 0 002 2h28a2 2 0 002-2v-4a2 2 0 00-2-2h-7v-8h32a5.006 5.006 0 005-5V36a5.006 5.006 0 00-5-5zM73 93v4H45v-4zm-9-2H54v-8h10zm37-13a3 3 0 01-3 3H22a3 3 0 01-3-3V36a3 3 0 013-3h76a3 3 0 013 3z"
        ></path>
        <path
          fill="#6576ff"
          d="M61.444 40H40.111L33 47.143V18.7a3.632 3.632 0 013.556-3.7h24.888A3.632 3.632 0 0165 18.7v17.6a3.632 3.632 0 01-3.556 3.7z"
        ></path>
        <path
          fill="none"
          stroke="#6576ff"
          strokeMiterlimit="10"
          strokeWidth="2"
          d="M61.444 40H40.111L33 47.143V18.7a3.632 3.632 0 013.556-3.7h24.888A3.632 3.632 0 0165 18.7v17.6a3.632 3.632 0 01-3.556 3.7z"
        ></path>
        <path
          fill="none"
          stroke="#fffffe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40 22L57 22"
        ></path>
        <path
          fill="none"
          stroke="#fffffe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40 27L57 27"
        ></path>
        <path
          fill="none"
          stroke="#fffffe"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth="2"
          d="M40 32L50 32"
        ></path>
        <path fill="none" stroke="#9cabff" strokeLinecap="round" strokeLinejoin="round" d="M30.5 87.5L30.5 91.5"></path>
        <path fill="none" stroke="#9cabff" strokeLinecap="round" strokeLinejoin="round" d="M28.5 89.5L32.5 89.5"></path>
        <path fill="none" stroke="#9cabff" strokeLinecap="round" strokeLinejoin="round" d="M79.5 22.5L79.5 26.5"></path>
        <path fill="none" stroke="#9cabff" strokeLinecap="round" strokeLinejoin="round" d="M77.5 24.5L81.5 24.5"></path>
        <circle cx="90.5" cy="97.5" r="3" fill="none" stroke="#9cabff" strokeMiterlimit="10"></circle>
        <circle cx="24" cy="23" r="2.5" fill="none" stroke="#9cabff" strokeMiterlimit="10"></circle>
      </svg>
    ),
    name: "Help/Support",
  },
];

export const fileManagerIconData = [
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" x="0" y="0" viewBox="0 0 72 72">
        <path
          fill="#6C87FE"
          d="M57.5 31h-23c-1.4 0-2.5-1.1-2.5-2.5v-10c0-1.4 1.1-2.5 2.5-2.5h23c1.4 0 2.5 1.1 2.5 2.5v10c0 1.4-1.1 2.5-2.5 2.5z"
        ></path>
        <path
          fill="#8AA3FF"
          d="M59.8 61H12.2C8.8 61 6 58 6 54.4V17.6C6 14 8.8 11 12.2 11h18.5c1.7 0 3.3 1 4.1 2.6L38 24h21.8c3.4 0 6.2 2.4 6.2 6v24.4c0 3.6-2.8 6.6-6.2 6.6z"
        ></path>
        <path fill="#798BFF" d="M7.7 59c2.2 2.4 4.7 2 6.3 2h45c1.1 0 3.2.1 5.3-2H7.7z"></path>
      </svg>
    ),
    name: "Folder",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="28" height="15" x="32" y="16" fill="#6c87fe" rx="2.5" ry="2.5"></rect>
        <path
          fill="#8aa3ff"
          d="M59.778 61H12.222A6.421 6.421 0 016 54.396V17.604A6.421 6.421 0 0112.222 11h18.476a4.671 4.671 0 014.113 2.564L38 24h21.778A5.91 5.91 0 0166 30v24.396A6.421 6.421 0 0159.778 61z"
        ></path>
        <path fill="#798bff" d="M8.015 59c2.169 2.383 4.698 2.016 6.195 2h44.57a6.277 6.277 0 005.207-2z"></path>
        <path
          fill="#4b66bc"
          d="M42.223 40H41.5v-2.556a5.5 5.5 0 00-11 0V40h-.723A2.801 2.801 0 0027 42.818v6.364A2.801 2.801 0 0029.777 52h12.446A2.801 2.801 0 0045 49.182v-6.364A2.801 2.801 0 0042.223 40zM36 48a2 2 0 112-2 2.002 2.002 0 01-2 2zm3.5-8h-7v-2.556a3.5 3.5 0 017 0z"
        ></path>
      </svg>
    ),
    name: "Folder Secure",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="28" height="15" x="32" y="16" fill="#6c87fe" rx="2.5" ry="2.5"></rect>
        <path
          fill="#8aa3ff"
          d="M59.778 61H12.222A6.421 6.421 0 016 54.396V17.604A6.421 6.421 0 0112.222 11h18.476a4.671 4.671 0 014.113 2.564L38 24h21.778A5.91 5.91 0 0166 30v24.396A6.421 6.421 0 0159.778 61z"
        ></path>
        <path fill="#798bff" d="M7.745 58.98C9.935 61.387 12.488 61.017 14 61h45a6.337 6.337 0 005.256-2.02z"></path>
        <path
          fill="#4b66bc"
          d="M29.63 37.36a3.024 3.024 0 01-.86-2.39A4.375 4.375 0 0132.997 31h.008a4.36 4.36 0 014.22 3.912 3.053 3.053 0 01-.855 2.448 4.416 4.416 0 01-3.139 1.17c-.077 0-.153-.002-.23-.005a4.519 4.519 0 01-3.37-1.165zm13.837 2.755a1 1 0 10-.934 1.77c.72.38 1.466 2.126 1.467 4.39V48a1 1 0 002 0v-1.726c-.001-2.93-.995-5.347-2.533-6.159zm-3.302-2.718c-.144.084-.29.168-.432.254a1 1 0 00.522 1.854.989.989 0 00.52-.147c.129-.078.26-.154.392-.23a4.231 4.231 0 002.146-2.124.984.984 0 00.031-.104 3.841 3.841 0 00-2.844-4.365 1 1 0 00-.492 1.94 1.877 1.877 0 011.4 1.909 2.835 2.835 0 01-1.243 1.013zM36.5 41h-7c-2.523 0-4.5 2.782-4.5 6.333V48.5a.836.836 0 00.059.291.973.973 0 00.35.495C26.466 50.281 29.462 51 33 51s6.535-.719 7.59-1.714a.973.973 0 00.35-.495.836.836 0 00.06-.291v-1.167C41 43.783 39.023 41 36.5 41z"
        ></path>
      </svg>
    ),
    name: "Folder Shared",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="28" height="15" x="32" y="16" fill="#f29611" rx="2.5" ry="2.5"></rect>
        <path
          fill="#ffb32c"
          d="M59.778 61H12.222A6.421 6.421 0 016 54.396V17.604A6.421 6.421 0 0112.222 11h18.476a4.671 4.671 0 014.113 2.564L38 24h21.778A5.91 5.91 0 0166 30v24.396A6.421 6.421 0 0159.778 61z"
        ></path>
        <path fill="#f2a222" d="M8.015 59c2.169 2.383 4.698 2.016 6.195 2h44.57a6.277 6.277 0 005.207-2z"></path>
      </svg>
    ),
    name: "Folder Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="28" height="15" x="32" y="16" fill="#f29611" rx="2.5" ry="2.5"></rect>
        <path
          fill="#ffb32c"
          d="M59.778 61H12.222A6.421 6.421 0 016 54.396V17.604A6.421 6.421 0 0112.222 11h18.476a4.671 4.671 0 014.113 2.564L38 24h21.778A5.91 5.91 0 0166 30v24.396A6.421 6.421 0 0159.778 61z"
        ></path>
        <path fill="#f2a222" d="M8.015 59c2.169 2.383 4.698 2.016 6.195 2h44.57a6.277 6.277 0 005.207-2z"></path>
        <path
          fill="#c67424"
          d="M42.223 40H41.5v-2.556a5.5 5.5 0 00-11 0V40h-.723A2.801 2.801 0 0027 42.818v6.364A2.801 2.801 0 0029.777 52h12.446A2.801 2.801 0 0045 49.182v-6.364A2.801 2.801 0 0042.223 40zM36 48a2 2 0 112-2 2.002 2.002 0 01-2 2zm3.5-8h-7v-2.556a3.5 3.5 0 017 0z"
        ></path>
      </svg>
    ),
    name: "Folder Secure Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="28" height="15" x="32" y="16" fill="#f29611" rx="2.5" ry="2.5"></rect>
        <path
          fill="#ffb32c"
          d="M59.778 61H12.222A6.421 6.421 0 016 54.396V17.604A6.421 6.421 0 0112.222 11h18.476a4.671 4.671 0 014.113 2.564L38 24h21.778A5.91 5.91 0 0166 30v24.396A6.421 6.421 0 0159.778 61z"
        ></path>
        <path fill="#f2a222" d="M8.015 59c2.169 2.383 4.698 2.016 6.195 2h44.57a6.277 6.277 0 005.207-2z"></path>
        <path
          fill="#c67424"
          d="M29.63 37.36a3.024 3.024 0 01-.86-2.39A4.375 4.375 0 0132.997 31h.008a4.36 4.36 0 014.22 3.912 3.053 3.053 0 01-.855 2.448 4.416 4.416 0 01-3.139 1.17c-.077 0-.153-.002-.23-.005a4.519 4.519 0 01-3.37-1.165zm13.837 2.74a1 1 0 10-.934 1.77c.72.38 1.466 2.126 1.467 4.39v1.726a1 1 0 002 0V46.26c-.001-2.93-.995-5.348-2.533-6.16zm-3.302-2.718c-.144.084-.29.168-.432.255a1 1 0 00.522 1.853.989.989 0 00.52-.147c.129-.078.26-.154.392-.23a4.231 4.231 0 002.146-2.124.984.984 0 00.031-.104A3.841 3.841 0 0040.5 32.52a1 1 0 10-.492 1.94 1.877 1.877 0 011.4 1.909 2.835 2.835 0 01-1.243 1.013zM36.5 41h-7c-2.523 0-4.5 2.782-4.5 6.333V48.5a.836.836 0 00.059.291.973.973 0 00.35.495C26.466 50.281 29.462 51 33 51s6.535-.719 7.59-1.714a.973.973 0 00.35-.495.836.836 0 00.06-.291v-1.167C41 43.783 39.023 41 36.5 41z"
        ></path>
      </svg>
    ),
    name: "Folder Shared Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#0089ff" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#9bd5f9" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <path
          fill="#fff"
          d="M44.709 39.72a2.007 2.007 0 01-.672-1.13 13.652 13.652 0 01-.177-2.588 20.143 20.143 0 00-.141-2.699 2.831 2.831 0 00-.568-1.365 2.392 2.392 0 00-1.104-.77 4.722 4.722 0 00-1.48-.168h-.583v1.636h.323a2.236 2.236 0 011.328.276 1.395 1.395 0 01.412.974q.052.396.052 2.428a7.437 7.437 0 00.49 3.183 3.521 3.521 0 001.688 1.516 3.246 3.246 0 00-1.417 1.078 4.133 4.133 0 00-.667 1.85q-.094.698-.094 3.303a2.866 2.866 0 01-.354 1.755 1.951 1.951 0 01-1.438.391h-.323v1.636h.584a5.15 5.15 0 001.292-.115 2.326 2.326 0 001.589-1.328 4.334 4.334 0 00.349-1.412q.052-.552.062-2.292a12.642 12.642 0 01.177-2.454 2.007 2.007 0 01.672-1.13 2.078 2.078 0 011.308-.438V40.16a2.078 2.078 0 01-1.308-.438zM30.146 39.94a3.95 3.95 0 00.64-1.72 30.109 30.109 0 00.115-3.448 2.842 2.842 0 01.354-1.745 1.951 1.951 0 011.438-.39h.323V31h-.583a5.635 5.635 0 00-1.292.104 2.315 2.315 0 00-1.59 1.334 4.366 4.366 0 00-.348 1.406q-.052.553-.063 2.293a12.753 12.753 0 01-.177 2.458 1.982 1.982 0 01-.672 1.13 2.096 2.096 0 01-1.308.433v1.698a2.078 2.078 0 011.308.438 2.009 2.009 0 01.672 1.136 13.753 13.753 0 01.177 2.594 20.138 20.138 0 00.141 2.699 2.797 2.797 0 00.568 1.36 2.477 2.477 0 001.104.776 4.712 4.712 0 001.48.167h.584V49.39h-.323a2.203 2.203 0 01-1.329-.281 1.464 1.464 0 01-.422-.99q-.041-.386-.042-2.418a7.392 7.392 0 00-.515-3.224 3.179 3.179 0 00-1.662-1.464 3.813 3.813 0 001.422-1.073z"
        ></path>
      </svg>
    ),
    name: "File Code",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#599def" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#c2e1ff" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <rect width="18" height="2" x="27" y="31" fill="#fff" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="36" fill="#fff" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="41" fill="#fff" rx="1" ry="1"></rect>
        <rect width="12" height="2" x="27" y="46" fill="#fff" rx="1" ry="1"></rect>
      </svg>
    ),
    name: "File Doc",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#755de0" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path
          fill="#fff"
          d="M27.222 43H44.71s2.325-.281.735-1.897l-5.603-5.498s-1.512-1.792-3.336.793L33.56 40.47a.689.689 0 01-1.019.048l-1.9-1.639s-1.329-1.587-2.475 0c-.656.908-2.026 2.849-2.026 2.849S25.427 43 27.222 43z"
        ></path>
        <path fill="#b5b3ff" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
      </svg>
    ),
    name: "File Media",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#f74141" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#ffa9a9" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <path
          fill="#fff"
          d="M46 46.5v-13a3.504 3.504 0 00-3.5-3.5h-13a3.504 3.504 0 00-3.5 3.5v13a3.504 3.504 0 003.5 3.5h13a3.504 3.504 0 003.5-3.5zM40 45v3h-3v-3zm-3-2v-6h7v6zm0-8v-3h3v3zm-2-3v3h-3v-3zm0 5v6h-7v-6zm0 8v3h-3v-3zm7.5 3H42v-3h2v1.5a1.502 1.502 0 01-1.5 1.5zM44 33.5V35h-2v-3h.5a1.502 1.502 0 011.5 1.5zM29.5 32h.5v3h-2v-1.5a1.502 1.502 0 011.5-1.5zM28 46.5V45h2v3h-.5a1.502 1.502 0 01-1.5-1.5z"
        ></path>
      </svg>
    ),
    name: "File Movie",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#5a5aff" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#b8b8ff" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <path
          fill="#f3f3f3"
          d="M44.647 30.237a1.006 1.006 0 00-.811-.223l-12 2A1 1 0 0031 33v9.556A3.954 3.954 0 0029 42a4 4 0 104 4V33.847l10-1.666v8.375A3.954 3.954 0 0041 40a4 4 0 104 4V31a.999.999 0 00-.353-.763zM29 48a2 2 0 112-2 2.002 2.002 0 01-2 2zm12-2a2 2 0 112-2 2.002 2.002 0 01-2 2z"
        ></path>
      </svg>
    ),
    name: "File Music",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#f26b6b" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#f4c9c9" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <path
          fill="#fff"
          d="M46.334 44.538a4.326 4.326 0 00-2.528-1.429 22.436 22.436 0 00-4.561-.383 19.356 19.356 0 01-3.425-4.772 56.508 56.508 0 001.375-6.086 2.339 2.339 0 00-.462-1.845 1.943 1.943 0 00-1.516-.753h-.001a1.685 1.685 0 00-1.39.697c-1.149 1.526-.363 5.219-.194 5.946a12.612 12.612 0 00.724 2.147 33.322 33.322 0 01-2.49 6.106 20.347 20.347 0 00-5.979 3.44 2.568 2.568 0 00-.886 1.827 1.802 1.802 0 00.634 1.306 2.061 2.061 0 001.395.531 2.244 2.244 0 001.459-.546 20.068 20.068 0 004.29-5.357 20.838 20.838 0 015.938-1.186 33.75 33.75 0 004.243 3.605 2.64 2.64 0 003.416-.236 2.08 2.08 0 00-.042-3.012zM27.62 49.623a.834.834 0 01-1.084.042.42.42 0 01-.167-.27c-.002-.066.027-.315.44-.736a18.038 18.038 0 013.762-2.368 17.26 17.26 0 01-2.95 3.332zm7.283-18.775a.343.343 0 01.315-.151.6.6 0 01.465.239.853.853 0 01.168.672c-.164.92-.424 2.38-.852 4.117l-.037-.151c-.356-1.523-.609-3.996-.059-4.726zm-1.179 12.703a34.973 34.973 0 001.52-3.767 21.248 21.248 0 002.224 3.05 21.857 21.857 0 00-3.744.717zm11.706 2.97a1.308 1.308 0 01-1.695.088 33.203 33.203 0 01-3.004-2.43 20.968 20.968 0 012.835.334 2.97 2.97 0 011.74.965c.533.633.296.87.123 1.043z"
        ></path>
      </svg>
    ),
    name: "File PDF",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#f25168" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#ff9fb6" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <path
          fill="#fff"
          d="M44.14 46H27.86A1.86 1.86 0 0126 44.14v-9.28A1.86 1.86 0 0127.86 33h16.28A1.86 1.86 0 0146 34.86v9.28A1.86 1.86 0 0144.14 46zm-14.995-2h13.71A1.145 1.145 0 0044 42.855v-6.71A1.145 1.145 0 0042.855 35h-13.71A1.145 1.145 0 0028 36.145v6.71A1.145 1.145 0 0029.145 44z"
        ></path>
        <path
          fill="#fff"
          d="M36.422 34.268a.711.711 0 01-.505-.21l-2.143-2.142a.714.714 0 011.01-1.01l2.143 2.143a.714.714 0 01-.505 1.22z"
        ></path>
        <path
          fill="#fff"
          d="M36.422 34.268a.714.714 0 01-.505-1.22l2.143-2.142a.714.714 0 011.01 1.01l-2.143 2.143a.711.711 0 01-.505.209zM32.136 49.268a.705.705 0 01-.367-.102.715.715 0 01-.245-.98l2.143-3.571a.714.714 0 011.225.735l-2.143 3.57a.714.714 0 01-.613.348zM40.708 49.268a.714.714 0 01-.613-.346l-2.142-3.572a.714.714 0 011.225-.735l2.142 3.572a.714.714 0 01-.612 1.081zM35.12 37H30.9a.5.5 0 110-1h4.22a.5.5 0 110 1zM41.976 43h-4.429a.506.506 0 110-1.006h4.429a.506.506 0 110 1.006zM38.14 40h-4.163a.5.5 0 110-1h4.163a.5.5 0 110 1z"
        ></path>
      </svg>
    ),
    name: "File PPT",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#36c684" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#95e5bd" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <path
          fill="#fff"
          d="M42 31H30a3.003 3.003 0 00-3 3v11a3.003 3.003 0 003 3h12a3.003 3.003 0 003-3V34a3.003 3.003 0 00-3-3zm-13 7h6v3h-6zm8 0h6v3h-6zm6-4v2h-6v-3h5a1.001 1.001 0 011 1zm-13-1h5v3h-6v-2a1.001 1.001 0 011-1zm-1 12v-2h6v3h-5a1.001 1.001 0 01-1-1zm13 1h-5v-3h6v2a1.001 1.001 0 01-1 1z"
        ></path>
      </svg>
    ),
    name: "File Sheet",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path fill="#7e95c4" d="M50 61H22a6 6 0 01-6-6V22l9-11h25a6 6 0 016 6v38a6 6 0 01-6 6z"></path>
        <path fill="#b7ccea" d="M25 20.556A1.444 1.444 0 0123.556 22H16l9-11z"></path>
        <rect width="18" height="2" x="27" y="31" fill="#fff" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="35" fill="#fff" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="39" fill="#fff" rx="1" ry="1"></rect>
        <rect width="14" height="2" x="27" y="43" fill="#fff" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="27" y="47" fill="#fff" rx="1" ry="1"></rect>
      </svg>
    ),
    name: "File Text",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="40" height="44" x="16" y="14" fill="#7e95c4" rx="6" ry="6"></rect>
        <rect width="8" height="2" x="32" y="17" fill="#fff" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="22" fill="#fff" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="27" fill="#fff" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="32" fill="#fff" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="37" fill="#fff" rx="1" ry="1"></rect>
        <path fill="#fff" d="M35 14h2v29a1 1 0 01-1 1 1 1 0 01-1-1V14z"></path>
        <path
          fill="#fff"
          d="M38.002 42h-4.004A1.998 1.998 0 0032 43.998v2.004A1.998 1.998 0 0033.998 48h4.004A1.998 1.998 0 0040 46.002v-2.004A1.998 1.998 0 0038.002 42zm-.005 4H34v-2h4z"
        ></path>
      </svg>
    ),
    name: "File Zip",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#0089ff"
          d="M44.709 39.72a2.007 2.007 0 01-.672-1.13 13.652 13.652 0 01-.177-2.588 20.143 20.143 0 00-.141-2.699 2.831 2.831 0 00-.568-1.365 2.392 2.392 0 00-1.104-.77 4.722 4.722 0 00-1.48-.168h-.583v1.636h.323a2.236 2.236 0 011.328.276 1.395 1.395 0 01.412.974q.052.396.052 2.428a7.437 7.437 0 00.49 3.183 3.521 3.521 0 001.688 1.516 3.246 3.246 0 00-1.417 1.078 4.133 4.133 0 00-.667 1.85q-.094.698-.094 3.303a2.866 2.866 0 01-.354 1.755 1.951 1.951 0 01-1.438.391h-.323v1.636h.584a5.15 5.15 0 001.292-.115 2.326 2.326 0 001.589-1.328 4.334 4.334 0 00.349-1.412q.052-.552.062-2.292a12.642 12.642 0 01.177-2.454 2.007 2.007 0 01.672-1.13 2.078 2.078 0 011.308-.438V40.16a2.078 2.078 0 01-1.308-.438zM30.146 39.94a3.95 3.95 0 00.64-1.72 30.109 30.109 0 00.115-3.448 2.842 2.842 0 01.354-1.745 1.951 1.951 0 011.438-.39h.323V31h-.583a5.635 5.635 0 00-1.292.104 2.315 2.315 0 00-1.59 1.334 4.366 4.366 0 00-.348 1.406q-.052.553-.063 2.293a12.753 12.753 0 01-.177 2.458 1.982 1.982 0 01-.672 1.13 2.096 2.096 0 01-1.308.433v1.698a2.078 2.078 0 011.308.438 2.009 2.009 0 01.672 1.136 13.753 13.753 0 01.177 2.594 20.138 20.138 0 00.141 2.699 2.797 2.797 0 00.568 1.36 2.477 2.477 0 001.104.776 4.712 4.712 0 001.48.167h.584V49.39h-.323a2.203 2.203 0 01-1.329-.281 1.464 1.464 0 01-.422-.99q-.041-.386-.042-2.418a7.392 7.392 0 00-.515-3.224 3.179 3.179 0 00-1.662-1.464 3.813 3.813 0 001.422-1.073z"
        ></path>
      </svg>
    ),
    name: "File Code Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <rect width="18" height="2" x="27" y="31" fill="#599def" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="36" fill="#599def" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="41" fill="#599def" rx="1" ry="1"></rect>
        <rect width="12" height="2" x="27" y="46" fill="#599def" rx="1" ry="1"></rect>
      </svg>
    ),
    name: "File Doc Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#755de0"
          d="M27.222 43H44.71s2.325-.281.735-1.897l-5.603-5.498s-1.512-1.792-3.336.793L33.56 40.47a.689.689 0 01-1.019.048l-1.9-1.639s-1.329-1.587-2.475 0c-.656.908-2.026 2.849-2.026 2.849S25.427 43 27.222 43z"
        ></path>
      </svg>
    ),
    name: "File Media Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#f74141"
          d="M46 46.5v-13a3.504 3.504 0 00-3.5-3.5h-13a3.504 3.504 0 00-3.5 3.5v13a3.504 3.504 0 003.5 3.5h13a3.504 3.504 0 003.5-3.5zM40 45v3h-3v-3zm-3-2v-6h7v6zm0-8v-3h3v3zm-2-3v3h-3v-3zm0 5v6h-7v-6zm0 8v3h-3v-3zm7.5 3H42v-3h2v1.5a1.502 1.502 0 01-1.5 1.5zM44 33.5V35h-2v-3h.5a1.502 1.502 0 011.5 1.5zM29.5 32h.5v3h-2v-1.5a1.502 1.502 0 011.5-1.5zM28 46.5V45h2v3h-.5a1.502 1.502 0 01-1.5-1.5z"
        ></path>
      </svg>
    ),
    name: "File Movie Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#5a5aff"
          d="M45.647 30.737a1.006 1.006 0 00-.811-.223l-12 2A1 1 0 0032 33.5v9.556a3.923 3.923 0 00-3.711-.17 3.863 3.863 0 00-2.082 2.313 4.025 4.025 0 005.37 4.997A3.977 3.977 0 0034 46.451V34.347l10-1.666v8.375a3.923 3.923 0 00-3.711-.17 3.863 3.863 0 00-2.082 2.313 4.025 4.025 0 005.37 4.997A3.977 3.977 0 0046 44.451V31.5a.999.999 0 00-.353-.763z"
        ></path>
      </svg>
    ),
    name: "File Music Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#f26b6b"
          d="M46.334 44.538a4.326 4.326 0 00-2.528-1.429 22.436 22.436 0 00-4.561-.383 19.356 19.356 0 01-3.425-4.772 56.508 56.508 0 001.375-6.086 2.339 2.339 0 00-.462-1.845 1.943 1.943 0 00-1.516-.753h-.001a1.685 1.685 0 00-1.39.697c-1.149 1.526-.363 5.219-.194 5.946a12.612 12.612 0 00.724 2.147 33.322 33.322 0 01-2.49 6.106 20.347 20.347 0 00-5.979 3.44 2.568 2.568 0 00-.886 1.827 1.802 1.802 0 00.634 1.306 2.061 2.061 0 001.395.531 2.244 2.244 0 001.459-.546 20.068 20.068 0 004.29-5.357 20.838 20.838 0 015.938-1.186 33.75 33.75 0 004.243 3.605 2.64 2.64 0 003.416-.236 2.08 2.08 0 00-.042-3.012zM27.62 49.623a.834.834 0 01-1.084.042.42.42 0 01-.167-.27c-.002-.066.027-.315.44-.736a18.038 18.038 0 013.762-2.368 17.26 17.26 0 01-2.95 3.332zm7.283-18.775a.343.343 0 01.315-.151.6.6 0 01.465.239.853.853 0 01.168.672c-.164.92-.424 2.38-.852 4.117l-.037-.151c-.356-1.523-.609-3.996-.059-4.726zm-1.179 12.703a34.973 34.973 0 001.52-3.767 21.248 21.248 0 002.224 3.05 21.857 21.857 0 00-3.744.717zm11.706 2.97a1.308 1.308 0 01-1.695.088 33.203 33.203 0 01-3.004-2.43 20.968 20.968 0 012.835.334 2.97 2.97 0 011.74.965c.533.633.296.87.123 1.043z"
        ></path>
      </svg>
    ),
    name: "File PDF Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#f25168"
          d="M44.14 46H27.86A1.86 1.86 0 0126 44.14v-9.28A1.86 1.86 0 0127.86 33h16.28A1.86 1.86 0 0146 34.86v9.28A1.86 1.86 0 0144.14 46zm-14.995-2h13.71A1.145 1.145 0 0044 42.855v-6.71A1.145 1.145 0 0042.855 35h-13.71A1.145 1.145 0 0028 36.145v6.71A1.145 1.145 0 0029.145 44z"
        ></path>
        <path
          fill="#f25168"
          d="M36.422 34.268a.711.711 0 01-.505-.21l-2.143-2.142a.714.714 0 011.01-1.01l2.143 2.143a.714.714 0 01-.505 1.22z"
        ></path>
        <path
          fill="#f25168"
          d="M36.422 34.268a.714.714 0 01-.505-1.22l2.143-2.142a.714.714 0 011.01 1.01l-2.143 2.143a.711.711 0 01-.505.209zM32.136 49.268a.705.705 0 01-.367-.102.715.715 0 01-.245-.98l2.143-3.571a.714.714 0 011.225.735l-2.143 3.57a.714.714 0 01-.613.348zM40.708 49.268a.714.714 0 01-.613-.346l-2.142-3.572a.714.714 0 011.225-.735l2.142 3.572a.714.714 0 01-.612 1.081zM35.12 37H30.9a.5.5 0 110-1h4.22a.5.5 0 110 1zM41.976 43h-4.429a.506.506 0 110-1.006h4.429a.506.506 0 110 1.006zM38.14 40h-4.163a.5.5 0 110-1h4.163a.5.5 0 110 1z"
        ></path>
      </svg>
    ),
    name: "File PPT Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <path
          fill="#36c684"
          d="M42 31H30a3.003 3.003 0 00-3 3v11a3.003 3.003 0 003 3h12a3.003 3.003 0 003-3V34a3.003 3.003 0 00-3-3zm-13 7h6v3h-6zm8 0h6v3h-6zm6-4v2h-6v-3h5a1.001 1.001 0 011 1zm-13-1h5v3h-6v-2a1.001 1.001 0 011-1zm-1 12v-2h6v3h-5a1.001 1.001 0 01-1-1zm13 1h-5v-3h6v2a1.001 1.001 0 01-1 1z"
        ></path>
      </svg>
    ),
    name: "File Sheet Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <path
          fill="#e3edfc"
          d="M49 61H23a5.015 5.015 0 01-5-5V16a5.015 5.015 0 015-5h17.91L54 22.111V56a5.015 5.015 0 01-5 5z"
        ></path>
        <path
          fill="#b7d0ea"
          d="M54 22.111h-9.818a3.303 3.303 0 01-3.273-3.333V11s1.841.208 6.955 4.583C52.84 20.097 54 22.111 54 22.111z"
        ></path>
        <path fill="#c4dbf2" d="M19.03 59A4.984 4.984 0 0023 61h26a4.984 4.984 0 003.97-2z"></path>
        <rect width="18" height="2" x="27" y="31" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="35" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="18" height="2" x="27" y="39" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="14" height="2" x="27" y="43" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="27" y="47" fill="#7e95c4" rx="1" ry="1"></rect>
      </svg>
    ),
    name: "File Text Alt",
  },
  {
    svg: (
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72">
        <rect width="36" height="40" x="18" y="16" fill="#e3edfc" rx="5" ry="5"></rect>
        <path fill="#c4dbf2" d="M19.03 54A4.984 4.984 0 0023 56h26a4.984 4.984 0 003.97-2z"></path>
        <rect width="8" height="2" x="32" y="20" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="25" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="30" fill="#7e95c4" rx="1" ry="1"></rect>
        <rect width="8" height="2" x="32" y="35" fill="#7e95c4" rx="1" ry="1"></rect>
        <path fill="#7e95c4" d="M35 16.06h2V41a1 1 0 01-1 1 1 1 0 01-1-1V16.06z"></path>
        <path
          fill="#7e95c4"
          d="M38.002 40h-4.004A1.998 1.998 0 0032 41.998v2.004A1.998 1.998 0 0033.998 46h4.004A1.998 1.998 0 0040 44.002v-2.004A1.998 1.998 0 0038.002 40zm-.005 4H34v-2h4z"
        ></path>
      </svg>
    ),
    name: "File Zip Alt",
  },
];
