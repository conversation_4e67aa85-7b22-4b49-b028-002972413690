{"apiEndpoints": ["/api/v1/map/get_rebranding", "/api/v1/login/get_rsa_key", "/api/v1/login/get_token", "/api/v1/sites/get", "/api/v1/data_displays/get", "/api/v1/groups/get", "/api/v1/login/renew", "/api/v1/users/get_icon", "/api/v1/groups/get_by_permission", "/api/v1/users/get"], "navigationItems": ["Dashboard", "Sites", "Map", "Data displays", "Groups", "Users", "Deleted sites"], "technology": {"frontend": "Quasar Framework (Vue.js)", "authentication": "RSA encryption + Bearer tokens", "architecture": "Single Page Application (SPA)", "apiStyle": "RESTful API"}, "features": {"authentication": {"description": "Secure login with RSA encryption", "endpoints": ["/api/v1/login/get_rsa_key", "/api/v1/login/get_token", "/api/v1/login/renew"]}, "siteManagement": {"description": "Monitoring site management and configuration", "endpoints": ["/api/v1/sites/get"]}, "userManagement": {"description": "User administration and permissions", "endpoints": ["/api/v1/users/get", "/api/v1/users/get_icon"]}, "groupManagement": {"description": "Group-based access control", "endpoints": ["/api/v1/groups/get", "/api/v1/groups/get_by_permission"]}, "dataDisplays": {"description": "Customizable data visualization dashboards", "endpoints": ["/api/v1/data_displays/get"]}, "mapping": {"description": "Geographic mapping with rebranding support", "endpoints": ["/api/v1/map/get_rebranding"]}}}