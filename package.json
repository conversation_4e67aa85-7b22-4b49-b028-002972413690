{"name": "hydroview", "version": "1.0.0", "description": "Hydroview - Radar-based water monitoring system", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "explore": "node website-explorer.js", "explore-enhanced": "node enhanced-explorer.js", "explore-deep": "node deep-navigation-explorer.js", "explore-sites": "node site-navigation-explorer.js", "explore-workflow": "node complete-site-workflow-explorer.js", "explore-detailed-tabs": "node detailed-site-tabs-explorer.js", "generate-workflow-docs": "node final-site-workflow-documentation.js", "analyze": "node analyze-report.js", "generate-docs": "node comprehensive-analysis.js"}, "author": "", "license": "ISC", "dependencies": {"@playwright/test": "^1.52.0", "playwright": "^1.52.0"}}