import React, { useState, useEffect } from "react";
import Head from "../layout/head/Head";
import Content from "../layout/content/Content";
import { 
  Block, 
  BlockHead, 
  BlockHeadContent, 
  BlockTitle, 
  BlockBetween,
  Row, 
  Col,
  Button,
  Icon
} from "../components/Component";
import { Card, CardBody, Badge, Progress, Modal, ModalHeader, ModalBody, ModalFooter, Form, FormGroup, Label, Input } from "reactstrap";

const Equipment = () => {
  const [equipment, setEquipment] = useState([]);
  const [filteredEquipment, setFilteredEquipment] = useState([]);
  const [filterType, setFilterType] = useState("all");
  const [filterStatus, setFilterStatus] = useState("all");
  const [showAddModal, setShowAddModal] = useState(false);
  const [newEquipment, setNewEquipment] = useState({
    name: "",
    type: "",
    site: "",
    model: "",
    serialNumber: "",
    installDate: "",
    status: "active"
  });

  useEffect(() => {
    // Load sample equipment data
    const sampleEquipment = [
      {
        id: 1,
        name: "Water Level Logger WL-001",
        type: "Logger",
        site: "Ganges - Haridwar",
        model: "AquaLog Pro 3000",
        serialNumber: "AL3000-2024-001",
        installDate: "2024-01-15",
        status: "active",
        batteryLevel: 85,
        lastMaintenance: "2024-05-15",
        nextMaintenance: "2024-08-15",
        dataTransmission: 98,
        location: "29.9457°N, 78.1642°E"
      },
      {
        id: 2,
        name: "Pressure Sensor PS-002",
        type: "Sensor",
        site: "Yamuna - Delhi",
        model: "HydroPressure X200",
        serialNumber: "HPX200-2024-002",
        installDate: "2024-02-10",
        status: "warning",
        batteryLevel: 45,
        lastMaintenance: "2024-04-10",
        nextMaintenance: "2024-07-10",
        dataTransmission: 92,
        location: "28.7041°N, 77.1025°E"
      },
      {
        id: 3,
        name: "Weather Station WS-003",
        type: "Weather Station",
        site: "Weather Station - Mumbai",
        model: "MeteoMax 5000",
        serialNumber: "MM5000-2024-003",
        installDate: "2024-01-20",
        status: "active",
        batteryLevel: 92,
        lastMaintenance: "2024-06-01",
        nextMaintenance: "2024-09-01",
        dataTransmission: 99,
        location: "19.0760°N, 72.8777°E"
      },
      {
        id: 4,
        name: "Flow Meter FM-004",
        type: "Flow Meter",
        site: "Krishna - Vijayawada",
        model: "FlowSense Ultra",
        serialNumber: "FSU-2024-004",
        installDate: "2024-03-05",
        status: "maintenance",
        batteryLevel: 0,
        lastMaintenance: "2024-06-05",
        nextMaintenance: "2024-06-20",
        dataTransmission: 0,
        location: "16.5062°N, 80.6480°E"
      },
      {
        id: 5,
        name: "Turbidity Sensor TS-005",
        type: "Sensor",
        site: "Narmada - Bharuch",
        model: "ClearWater T100",
        serialNumber: "CWT100-2024-005",
        installDate: "2024-02-28",
        status: "active",
        batteryLevel: 78,
        lastMaintenance: "2024-05-28",
        nextMaintenance: "2024-08-28",
        dataTransmission: 95,
        location: "21.7051°N, 72.9959°E"
      },
      {
        id: 6,
        name: "Data Logger DL-006",
        type: "Logger",
        site: "Ganges - Haridwar",
        model: "DataVault 2000",
        serialNumber: "DV2000-2024-006",
        installDate: "2024-04-01",
        status: "inactive",
        batteryLevel: 15,
        lastMaintenance: "2024-04-01",
        nextMaintenance: "2024-07-01",
        dataTransmission: 25,
        location: "29.9457°N, 78.1642°E"
      }
    ];
    
    setEquipment(sampleEquipment);
    setFilteredEquipment(sampleEquipment);
  }, []);

  useEffect(() => {
    // Filter equipment based on type and status
    let filtered = equipment;
    
    if (filterType !== "all") {
      filtered = filtered.filter(eq => eq.type.toLowerCase().replace(" ", "_") === filterType);
    }
    
    if (filterStatus !== "all") {
      filtered = filtered.filter(eq => eq.status === filterStatus);
    }
    
    setFilteredEquipment(filtered);
  }, [equipment, filterType, filterStatus]);

  const getStatusBadge = (status) => {
    switch (status) {
      case "active":
        return <Badge color="success">Active</Badge>;
      case "warning":
        return <Badge color="warning">Warning</Badge>;
      case "maintenance":
        return <Badge color="info">Maintenance</Badge>;
      case "inactive":
        return <Badge color="danger">Inactive</Badge>;
      default:
        return <Badge color="secondary">Unknown</Badge>;
    }
  };

  const getBatteryColor = (level) => {
    if (level > 60) return "success";
    if (level > 30) return "warning";
    return "danger";
  };

  const getTransmissionColor = (rate) => {
    if (rate > 90) return "success";
    if (rate > 70) return "warning";
    return "danger";
  };

  const handleAddEquipment = () => {
    const equipment_item = {
      id: equipment.length + 1,
      ...newEquipment,
      batteryLevel: 100,
      lastMaintenance: new Date().toISOString().split('T')[0],
      nextMaintenance: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      dataTransmission: 100,
      location: "0.0000°N, 0.0000°E"
    };
    
    setEquipment([...equipment, equipment_item]);
    setNewEquipment({
      name: "",
      type: "",
      site: "",
      model: "",
      serialNumber: "",
      installDate: "",
      status: "active"
    });
    setShowAddModal(false);
  };

  const equipmentStats = {
    total: equipment.length,
    active: equipment.filter(eq => eq.status === "active").length,
    warning: equipment.filter(eq => eq.status === "warning").length,
    maintenance: equipment.filter(eq => eq.status === "maintenance").length,
    lowBattery: equipment.filter(eq => eq.batteryLevel < 30).length
  };

  const equipmentTypes = [
    { id: "all", name: "All Types" },
    { id: "logger", name: "Logger" },
    { id: "sensor", name: "Sensor" },
    { id: "weather_station", name: "Weather Station" },
    { id: "flow_meter", name: "Flow Meter" }
  ];

  const statusOptions = [
    { id: "all", name: "All Status" },
    { id: "active", name: "Active" },
    { id: "warning", name: "Warning" },
    { id: "maintenance", name: "Maintenance" },
    { id: "inactive", name: "Inactive" }
  ];

  return (
    <React.Fragment>
      <Head title="Equipment Management - HydroTrack"></Head>
      <Content>
        <BlockHead size="sm">
          <BlockBetween>
            <BlockHeadContent>
              <BlockTitle page>Equipment Management</BlockTitle>
              <p className="text-soft">
                Monitor and manage monitoring equipment across all sites
              </p>
            </BlockHeadContent>
            <BlockHeadContent>
              <div className="toggle-wrap nk-block-tools-toggle">
                <Button color="primary" onClick={() => setShowAddModal(true)}>
                  <Icon name="plus"></Icon>
                  <span>Add Equipment</span>
                </Button>
              </div>
            </BlockHeadContent>
          </BlockBetween>
        </BlockHead>

        {/* Equipment Statistics */}
        <Block>
          <Row className="g-gs">
            <Col xxl="2" md="4" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Total Equipment</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4">{equipmentStats.total}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-primary">
                          <Icon name="server"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="2" md="4" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Active</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-success">{equipmentStats.active}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-success">
                          <Icon name="check-circle"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="2" md="4" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Warning</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-warning">{equipmentStats.warning}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-warning">
                          <Icon name="alert-triangle"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="2" md="4" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Maintenance</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-info">{equipmentStats.maintenance}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-info">
                          <Icon name="tool"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>

            <Col xxl="2" md="4" sm="6">
              <Card className="card-bordered">
                <CardBody className="card-inner">
                  <div className="card-title-group align-start mb-2">
                    <div className="card-title">
                      <h6 className="title">Low Battery</h6>
                    </div>
                  </div>
                  <div className="align-end flex-sm-wrap g-4 flex-md-nowrap">
                    <div className="nk-sale-data">
                      <span className="amount h4 text-danger">{equipmentStats.lowBattery}</span>
                    </div>
                    <div className="nk-sales-ck ms-auto">
                      <div className="sales-ck-wrap">
                        <div className="text-danger">
                          <Icon name="battery"></Icon>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Block>

        {/* Filters */}
        <Block>
          <Card className="card-bordered">
            <CardBody className="card-inner">
              <div className="card-title-group">
                <div className="card-title">
                  <h6 className="title">Filter Equipment</h6>
                </div>
              </div>
              <Row className="g-3">
                <Col md="4">
                  <FormGroup>
                    <Label>Equipment Type</Label>
                    <Input 
                      type="select" 
                      value={filterType} 
                      onChange={(e) => setFilterType(e.target.value)}
                    >
                      {equipmentTypes.map(type => (
                        <option key={type.id} value={type.id}>{type.name}</option>
                      ))}
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="4">
                  <FormGroup>
                    <Label>Status</Label>
                    <Input 
                      type="select" 
                      value={filterStatus} 
                      onChange={(e) => setFilterStatus(e.target.value)}
                    >
                      {statusOptions.map(status => (
                        <option key={status.id} value={status.id}>{status.name}</option>
                      ))}
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="4">
                  <FormGroup>
                    <Label>&nbsp;</Label>
                    <div>
                      <Button color="secondary" outline onClick={() => {
                        setFilterType("all");
                        setFilterStatus("all");
                      }}>
                        Clear Filters
                      </Button>
                    </div>
                  </FormGroup>
                </Col>
              </Row>
            </CardBody>
          </Card>
        </Block>

        {/* Equipment List */}
        <Block>
          <Row className="g-gs">
            {filteredEquipment.map((item) => (
              <Col key={item.id} xxl="6" lg="6">
                <Card className="card-bordered">
                  <CardBody className="card-inner">
                    <div className="card-title-group align-start mb-3">
                      <div className="card-title">
                        <h6 className="title">{item.name}</h6>
                        <p className="text-soft">{item.model} - {item.serialNumber}</p>
                      </div>
                      <div className="card-tools">
                        {getStatusBadge(item.status)}
                      </div>
                    </div>
                    
                    <div className="row g-3 mb-3">
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Type</label>
                          <div className="form-control-wrap">
                            <Badge color="outline-primary">{item.type}</Badge>
                          </div>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Site</label>
                          <div className="form-control-wrap">
                            <span>{item.site}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="row g-3 mb-3">
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Battery Level</label>
                          <div className="form-control-wrap">
                            <Progress 
                              value={item.batteryLevel} 
                              color={getBatteryColor(item.batteryLevel)}
                            />
                            <span className="form-note">{item.batteryLevel}%</span>
                          </div>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Data Transmission</label>
                          <div className="form-control-wrap">
                            <Progress 
                              value={item.dataTransmission} 
                              color={getTransmissionColor(item.dataTransmission)}
                            />
                            <span className="form-note">{item.dataTransmission}%</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="row g-3 mb-3">
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Last Maintenance</label>
                          <div className="form-control-wrap">
                            <span>{item.lastMaintenance}</span>
                          </div>
                        </div>
                      </div>
                      <div className="col-6">
                        <div className="form-group">
                          <label className="form-label text-soft">Next Maintenance</label>
                          <div className="form-control-wrap">
                            <span className="text-primary">{item.nextMaintenance}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="card-tools mt-3">
                      <ul className="btn-toolbar gx-1">
                        <li>
                          <Button size="sm" color="primary" outline>
                            <Icon name="eye"></Icon>
                            <span>View Details</span>
                          </Button>
                        </li>
                        <li>
                          <Button size="sm" color="warning" outline>
                            <Icon name="tool"></Icon>
                            <span>Maintenance</span>
                          </Button>
                        </li>
                        <li>
                          <Button size="sm" color="gray" outline>
                            <Icon name="edit"></Icon>
                            <span>Edit</span>
                          </Button>
                        </li>
                      </ul>
                    </div>
                  </CardBody>
                </Card>
              </Col>
            ))}
          </Row>
        </Block>

        {/* Add Equipment Modal */}
        <Modal isOpen={showAddModal} toggle={() => setShowAddModal(false)} size="lg">
          <ModalHeader toggle={() => setShowAddModal(false)}>
            Add New Equipment
          </ModalHeader>
          <ModalBody>
            <Form>
              <Row>
                <Col md="6">
                  <FormGroup>
                    <Label>Equipment Name</Label>
                    <Input 
                      type="text" 
                      placeholder="e.g., Water Level Logger WL-007"
                      value={newEquipment.name}
                      onChange={(e) => setNewEquipment({...newEquipment, name: e.target.value})}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label>Type</Label>
                    <Input 
                      type="select" 
                      value={newEquipment.type}
                      onChange={(e) => setNewEquipment({...newEquipment, type: e.target.value})}
                    >
                      <option value="">Select Type</option>
                      <option value="Logger">Logger</option>
                      <option value="Sensor">Sensor</option>
                      <option value="Weather Station">Weather Station</option>
                      <option value="Flow Meter">Flow Meter</option>
                    </Input>
                  </FormGroup>
                </Col>
              </Row>
              
              <Row>
                <Col md="6">
                  <FormGroup>
                    <Label>Site</Label>
                    <Input 
                      type="select" 
                      value={newEquipment.site}
                      onChange={(e) => setNewEquipment({...newEquipment, site: e.target.value})}
                    >
                      <option value="">Select Site</option>
                      <option value="Ganges - Haridwar">Ganges - Haridwar</option>
                      <option value="Yamuna - Delhi">Yamuna - Delhi</option>
                      <option value="Weather Station - Mumbai">Weather Station - Mumbai</option>
                      <option value="Krishna - Vijayawada">Krishna - Vijayawada</option>
                      <option value="Narmada - Bharuch">Narmada - Bharuch</option>
                    </Input>
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label>Model</Label>
                    <Input 
                      type="text" 
                      placeholder="e.g., AquaLog Pro 3000"
                      value={newEquipment.model}
                      onChange={(e) => setNewEquipment({...newEquipment, model: e.target.value})}
                    />
                  </FormGroup>
                </Col>
              </Row>
              
              <Row>
                <Col md="6">
                  <FormGroup>
                    <Label>Serial Number</Label>
                    <Input 
                      type="text" 
                      placeholder="e.g., AL3000-2024-007"
                      value={newEquipment.serialNumber}
                      onChange={(e) => setNewEquipment({...newEquipment, serialNumber: e.target.value})}
                    />
                  </FormGroup>
                </Col>
                <Col md="6">
                  <FormGroup>
                    <Label>Install Date</Label>
                    <Input 
                      type="date" 
                      value={newEquipment.installDate}
                      onChange={(e) => setNewEquipment({...newEquipment, installDate: e.target.value})}
                    />
                  </FormGroup>
                </Col>
              </Row>
            </Form>
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={() => setShowAddModal(false)}>
              Cancel
            </Button>
            <Button color="primary" onClick={handleAddEquipment}>
              Add Equipment
            </Button>
          </ModalFooter>
        </Modal>
      </Content>
    </React.Fragment>
  );
};

export default Equipment;
