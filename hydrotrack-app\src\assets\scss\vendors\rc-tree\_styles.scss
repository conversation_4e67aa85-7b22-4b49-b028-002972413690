$icons : url('data:image/png;base64,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');
$icons_dark : url('data:image/png;base64,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');
.rc-tree {
  isolation: isolate;
  margin: 0;
  border: 1px solid transparent;
}
.rc-tree-list{
  border: none !important;
}
  .rc-tree-focused:not(.rc-tree-active-focused) {
    border-color: cyan;
  }
  .rc-tree .rc-tree-treenode {
    margin: 0;
    padding: 0;
    line-height: 24px;
    white-space: nowrap;
    list-style: none;
    outline: 0;
    display: inline-flex;
    position: relative;
  }
  .rc-tree .rc-tree-treenode .draggable {
    color: #333;
    -moz-user-select: none;
    -khtml-user-select: none;
    -webkit-user-select: none;
    user-select: none;
    /* Required to make elements draggable in old WebKit */
  }
  .rc-tree .rc-tree-treenode.dragging {
    background: rgba(100, 100, 255, 0.1);
  }
  .rc-tree .rc-tree-treenode.drop-container > .draggable::after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    box-shadow: inset 0 0 0 2px red;
    content: '';
  }
  .rc-tree .rc-tree-treenode.drop-container ~ .rc-tree-treenode {
    border-left: 2px solid chocolate;
  }
  .rc-tree .rc-tree-treenode.drop-target {
    background-color: yellowgreen;
  }
  .rc-tree .rc-tree-treenode.drop-target ~ .rc-tree-treenode {
    border-left: none;
  }
  .rc-tree .rc-tree-treenode.filter-node > .rc-tree-node-content-wrapper {
    color: #a60000 !important;
    font-weight: bold !important;
  }
  .rc-tree .rc-tree-treenode ul {
    margin: 0;
    padding: 0 0 0 18px;
  }
  .rc-tree .rc-tree-treenode .rc-tree-node-content-wrapper {
    position: relative;
    display: inline-flex;
    height: 24px;
    margin: 0;
    padding: 0;
    text-decoration: none;
    vertical-align: top;
    cursor: pointer;
    padding-inline-end: 4px;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-switcher,
  .rc-tree .rc-tree-treenode span.rc-tree-checkbox,
  .rc-tree .rc-tree-treenode span.rc-tree-iconEle {
    display: inline-block;
    width: 24px;
    height: 24px;
    margin-right: 2px;
    line-height: 24px;
    vertical-align: -0.125em;
    background-color: transparent;
    background-image: $icons;
    background-repeat: no-repeat;
    background-attachment: scroll;
    border: 0 none;
    outline: none;
    cursor: pointer;
    .dark-mode &{
      background-image: $icons_dark;
    }
  }
  .rc-tree .rc-tree-treenode span.rc-tree-switcher.rc-tree-icon__customize,
  .rc-tree .rc-tree-treenode span.rc-tree-checkbox.rc-tree-icon__customize,
  .rc-tree .rc-tree-treenode span.rc-tree-iconEle.rc-tree-icon__customize {
    background-image: none !important;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-icon_loading {
    margin-right: 2px;
    vertical-align: top;
    background: url('data:image/gif;base64,R0lGODlhEAAQAKIGAMLY8YSx5HOm4Mjc88/g9Ofw+v///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgAGACwAAAAAEAAQAAADMGi6RbUwGjKIXCAA016PgRBElAVlG/RdLOO0X9nK61W39qvqiwz5Ls/rRqrggsdkAgAh+QQFCgAGACwCAAAABwAFAAADD2hqELAmiFBIYY4MAutdCQAh+QQFCgAGACwGAAAABwAFAAADD1hU1kaDOKMYCGAGEeYFCQAh+QQFCgAGACwKAAIABQAHAAADEFhUZjSkKdZqBQG0IELDQAIAIfkEBQoABgAsCgAGAAUABwAAAxBoVlRKgyjmlAIBqCDCzUoCACH5BAUKAAYALAYACgAHAAUAAAMPaGpFtYYMAgJgLogA610JACH5BAUKAAYALAIACgAHAAUAAAMPCAHWFiI4o1ghZZJB5i0JACH5BAUKAAYALAAABgAFAAcAAAMQCAFmIaEp1motpDQySMNFAgA7') no-repeat scroll 0 0 transparent;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-switcher.rc-tree-switcher-noop {
    cursor: auto;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-switcher.rc-tree-switcher_open {
    background-position: -36px -4px;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-switcher.rc-tree-switcher_close {
    background-position: -4px -4px;
  }
  .rc-tree.rc-tree-show-line .rc-tree-treenode span.rc-tree-switcher.rc-tree-switcher_open {
    background-position: -132px -4px;
  }
  .rc-tree.rc-tree-show-line .rc-tree-treenode span.rc-tree-switcher.rc-tree-switcher_close {
    background-position: -100px -4px;
  }
  .rc-tree {
    .rc-tree-treenode span.rc-tree-checkbox {
      width: 24px;
      height: 24px;
      margin: 0 3px;
      background-position: -164px -4px;
    }
    .rc-tree-treenode span.rc-tree-checkbox-checked {
      background-position: -228px -4px;
    }
    .rc-tree-treenode span.rc-tree-checkbox-indeterminate {
      background-position: -196px -4px;
    }
    .rc-tree-treenode span.rc-tree-checkbox-disabled {
      background-position: -164px -36px;
      opacity: .5;
    }
    .rc-tree-treenode span.rc-tree-checkbox.rc-tree-checkbox-checked.rc-tree-checkbox-disabled {
      background-position: -228px -4px;
    }
    &.rc-tree-show-line{
      .rc-tree-treenode span.rc-tree-checkbox {
        background-position: -167px -4px;
        margin: 0;
      }
      .rc-tree-treenode span.rc-tree-checkbox-checked {
        background-position: -231px -4px;
      }
      .rc-tree-treenode span.rc-tree-checkbox-indeterminate {
        background-position: -197px -4px;
      }
      .rc-tree-treenode span.rc-tree-checkbox-disabled {
        background-position: -167px -36px;
        opacity: .5;
      }
      .rc-tree-treenode span.rc-tree-checkbox.rc-tree-checkbox-checked.rc-tree-checkbox-disabled {
        background-position: -231px -4px;
      }
    }
  }
  .rc-tree .rc-tree-treenode span.rc-tree-checkbox.rc-tree-checkbox-indeterminate.rc-tree-checkbox-disabled {
    position: relative;
    background: #ccc;
    border-radius: 3px;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-checkbox.rc-tree-checkbox-indeterminate.rc-tree-checkbox-disabled::after {
    position: absolute;
    top: 5px;
    left: 3px;
    width: 5px;
    height: 0;
    border: 2px solid #fff;
    border-top: 0;
    border-left: 0;
    -webkit-transform: scale(1);
    transform: scale(1);
    content: ' ';
  }
  .rc-tree:not(.rc-tree-show-line) .rc-tree-treenode .rc-tree-switcher-noop {
    background-image: none !important;
  }
  // .rc-tree.rc-tree-show-line .rc-tree-treenode:not(:last-child) > ul {
  //   background: url('data:image/gif;base64,R0lGODlhCQACAIAAAMzMzP///yH5BAEAAAEALAAAAAAJAAIAAAIEjI9pUAA7') 0 0 repeat-y;
  // }
  .rc-tree.rc-tree-show-line .rc-tree-treenode:not(:last-child) > .rc-tree-switcher-noop {
    background-position: -133px -68px;
  }
  .rc-tree.rc-tree-show-line .rc-tree-treenode:last-child > .rc-tree-switcher-noop {
    background-position: -68px -4px;
  }
  .rc-tree-child-tree {
    display: none;
  }
  .rc-tree-child-tree-open {
    display: block;
  }
  .rc-tree-treenode-disabled > span:not(.rc-tree-switcher),
  .rc-tree-treenode-disabled > a,
  .rc-tree-treenode-disabled > a span {
    color: #767676;
    cursor: not-allowed;
  }
  .rc-tree-treenode-active {
    background: rgba(0, 0, 0, 0.1);
  }
  .rc-tree-node-selected {
    background: $light !important;
    border-radius: 2px !important;
    .dark-mode &{
      background: lighten($darker, 10%) !important;
    }
  }
  .rc-tree-icon__open {
    margin-right: 2px;
    vertical-align: top;
    background-position: -260px -4px;
  }
  .rc-tree-icon__close {
    margin-right: 2px;
    vertical-align: top;
    background-position: -260px -4px;
  }
  .rc-tree-icon__docu {
    margin-right: 2px;
    vertical-align: top;
    background-position: -102px -68px;
  }
  .rc-tree-icon__customize {
    margin-right: 2px;
    vertical-align: top;
    font-size: 1rem;
  }
  .rc-tree .rc-tree-treenode span.rc-tree-iconEle.rc-tree-icon__customize {
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
  .rc-tree-title {
    display: inline-block;
    .dark-mode &{
      color: $base-400;
    }
    .dark-mode .rc-tree-treenode-disabled &{
      color: $base-500;
    }
  }
  .rc-tree-indent {
    display: inline-block;
    height: 24px;
    vertical-align: bottom;
  }
  .rc-tree-indent-unit {
    display: inline-block;
    width: 24px;
  }
  // .rc-tree-indent {
  //   display: inline-block;
  //   height: 100%;
  //   vertical-align: bottom;
  // }
  .rc-tree.rc-tree-show-line .rc-tree-indent-unit {
    height: 24px;
    background-image: $icons;
    background-position: -292px -4px;
    .dark-mode &{
      background-image: $icons_dark;
    }
    &-end{
      opacity: 0;
    }
  }
  .rc-tree-draggable-icon {
    display: inline-flex;
    justify-content: center;
    width: 24px;
  }
  