<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>DGPS Points Map</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <style>
        html, body, #map {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        .elev-label {
            font-size: 10px;
            font-weight: bold;
            background: rgba(255,255,255,0.7);
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div id="map"></div>

    <!-- Leaflet JS -->
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/proj4js/2.7.5/proj4.js"></script>

    <script>
        // Initialize map
       const map = L.map('map', {
    maxZoom: 25
});

<PERSON><PERSON>tileLayer('https://{s}.google.com/vt/lyrs=s&x={x}&y={y}&z={z}', {
    maxZoom: 25,
    subdomains: ['mt0', 'mt1', 'mt2', 'mt3'],
    attribution: '&copy; Google Satellite'
}).addTo(map);

        // UTM to LatLng converter (UTM Zone 43N)
        proj4.defs("EPSG:32643", "+proj=utm +zone=43 +datum=WGS84 +units=m +no_defs");
        const utm43n = proj4("EPSG:32643");
        const wgs84 = proj4("WGS84");

        // Points: [y=Northing, x=Easting]
        const rawPoints = [
            { name: "base_1", y: 2478751.89, x: 333247.089, elev: 101.808 },
            { name: "Dami TBM", y: 2478754.274, x: 333253.132, elev: 100 },
			{ name: "TBM", y: 2478754.217, x: 333253.124, elev: 100.01 },
			{ name: "GL", y: 2478767.34, x: 333230.766, elev: 98.527 },
            { name: "GL", y: 2478768.541, x: 333230.698, elev: 97.511 },
			{ name: "GL", y: 2478768.994, x: 333231.603, elev: 97.568 },
			{ name: "GL", y: 2478770.436, x: 333232.198, elev: 97.208 },
			 { name: "GL", y: 2478774.991, x: 333234.065, elev: 96.769 },
            { name: "GL", y: 2478775.982, x: 333235.195, elev: 96.631 },
			{ name: "GL", y: 2478779.29, x: 333236.509, elev: 96.362 },
            { name: "GL", y: 2478781.028, x: 333238.212, elev: 96.187 },
            { name: "GL", y: 2478782.196, x: 333238.507, elev: 96.526 },
            { name: "GL", y: 2478783.461, x: 333239.346, elev: 96.334 },
            { name: "GL", y: 2478784.867, x: 333239.987, elev: 96.118 },
            { name: "GL", y: 2478786.049, x: 333240.255, elev: 95.914 },
            { name: "GL", y: 2478791.572, x: 333242.945, elev: 95.931 },
            { name: "GL", y: 2478798.476, x: 333246.353, elev: 95.926 },
            { name: "GL", y: 2478808.17, x: 333251.102, elev: 95.899 },
            { name: "GL", y: 2478817.91, x: 333255.825, elev: 95.904 },
            { name: "GL", y: 2478828.728, x: 333261.125, elev: 95.923 },
            { name: "GL", y: 2478841.352, x: 333267.22, elev: 95.925 },
            { name: "GL", y: 2478853.15, x: 333272.971, elev: 95.895 },
            { name: "GL", y: 2478869.001, x: 333280.688, elev: 95.929 },
            { name: "GL", y: 2478880.063, x: 333286.149, elev: 95.91 },
            { name: "GL", y: 2478889.086, x: 333290.607, elev: 95.91 },
            { name: "GL", y: 2478898.999, x: 333295.355, elev: 95.928 },
            { name: "GL", y: 2478910.344, x: 333300.886, elev: 95.933 },
            { name: "GL", y: 2478920.929, x: 333306.096, elev: 95.914 },
            { name: "GL", y: 2478931.472, x: 333311.316, elev: 95.913 },
            { name: "GL", y: 2478939.833, x: 333315.363, elev: 95.923 },
            { name: "GL", y: 2478951.024, x: 333320.817, elev: 95.91 },
            { name: "GL", y: 2478959.839, x: 333325.115, elev: 95.913 },
            { name: "GL", y: 2478969.128, x: 333329.602, elev: 95.91 },
            { name: "GL", y: 2478974.03, x: 333332.067, elev: 95.908 },
            { name: "GL", y: 2478978.347, x: 333334.165, elev: 95.924 },
            { name: "GL", y: 2478981.979, x: 333334.857, elev: 96.197 },
            { name: "GL", y: 2478987.451, x: 333338.538, elev: 96.559 },
            { name: "GL", y: 2478992.301, x: 333340.934, elev: 97.066 },
			{ name: "GL", y: 2478992.798, x: 333341.42, elev: 97.179 },
			{ name: "GL", y: 2478995.725, x: 333342.914, elev: 98.684 },
            { name: "GL", y: 2478997.127, x: 333343.707, elev: 97.961 },
            { name: "GL", y: 2479004.306, x: 333347.85, elev: 98.474 },
            { name: "GL", y: 2478959.392, x: 333358.768, elev: 96.769 },
            { name: "GL", y: 2478809.766, x: 333285.793, elev: 96.685 },
            { name: "GL", y: 2478809.731, x: 333282.732, elev: 95.901 },
            
        ];

        const latlngs = [];

        rawPoints.forEach(p => {
            const [lon, lat] = proj4(utm43n, wgs84, [p.x, p.y]);
            const marker = L.circleMarker([lat, lon], {
                radius: 4,
                color: "#007bff",
                fillColor: "#007bff",
                fillOpacity: 0.9,
                weight: 1
            }).addTo(map);

            marker.bindTooltip(`${p.elev} m`, {
                permanent: true,
                direction: 'top',
                className: 'elev-label',
                offset: [0, -6]
            });

            latlngs.push([lat, lon]);
        });

        // Draw polyline connecting the points
        const polyline = L.polyline(latlngs, {
            color: "red",
            weight: 2,
            opacity: 1
        }).addTo(map);

        // Fit map to polyline
        map.fitBounds(polyline.getBounds());
    </script>
</body>
</html>
