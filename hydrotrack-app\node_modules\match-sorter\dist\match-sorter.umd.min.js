!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e="undefined"!=typeof globalThis?globalThis:e||self).matchSorter={})}(this,(function(e){"use strict";var n={exports:{}},t={"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","Ấ":"A","Ắ":"A","Ẳ":"A","Ẵ":"A","Ặ":"A","Æ":"AE","Ầ":"A","Ằ":"A","Ȃ":"A","Ả":"A","Ạ":"A","Ẩ":"A","Ẫ":"A","Ậ":"A","Ç":"C","Ḉ":"C","È":"E","É":"E","Ê":"E","Ë":"E","Ế":"E","Ḗ":"E","Ề":"E","Ḕ":"E","Ḝ":"E","Ȇ":"E","Ẻ":"E","Ẽ":"E","Ẹ":"E","Ể":"E","Ễ":"E","Ệ":"E","Ì":"I","Í":"I","Î":"I","Ï":"I","Ḯ":"I","Ȋ":"I","Ỉ":"I","Ị":"I","Ð":"D","Ñ":"N","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","Ố":"O","Ṍ":"O","Ṓ":"O","Ȏ":"O","Ỏ":"O","Ọ":"O","Ổ":"O","Ỗ":"O","Ộ":"O","Ờ":"O","Ở":"O","Ỡ":"O","Ớ":"O","Ợ":"O","Ù":"U","Ú":"U","Û":"U","Ü":"U","Ủ":"U","Ụ":"U","Ử":"U","Ữ":"U","Ự":"U","Ý":"Y","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","ấ":"a","ắ":"a","ẳ":"a","ẵ":"a","ặ":"a","æ":"ae","ầ":"a","ằ":"a","ȃ":"a","ả":"a","ạ":"a","ẩ":"a","ẫ":"a","ậ":"a","ç":"c","ḉ":"c","è":"e","é":"e","ê":"e","ë":"e","ế":"e","ḗ":"e","ề":"e","ḕ":"e","ḝ":"e","ȇ":"e","ẻ":"e","ẽ":"e","ẹ":"e","ể":"e","ễ":"e","ệ":"e","ì":"i","í":"i","î":"i","ï":"i","ḯ":"i","ȋ":"i","ỉ":"i","ị":"i","ð":"d","ñ":"n","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","ố":"o","ṍ":"o","ṓ":"o","ȏ":"o","ỏ":"o","ọ":"o","ổ":"o","ỗ":"o","ộ":"o","ờ":"o","ở":"o","ỡ":"o","ớ":"o","ợ":"o","ù":"u","ú":"u","û":"u","ü":"u","ủ":"u","ụ":"u","ử":"u","ữ":"u","ự":"u","ý":"y","ÿ":"y","Ā":"A","ā":"a","Ă":"A","ă":"a","Ą":"A","ą":"a","Ć":"C","ć":"c","Ĉ":"C","ĉ":"c","Ċ":"C","ċ":"c","Č":"C","č":"c","C̆":"C","c̆":"c","Ď":"D","ď":"d","Đ":"D","đ":"d","Ē":"E","ē":"e","Ĕ":"E","ĕ":"e","Ė":"E","ė":"e","Ę":"E","ę":"e","Ě":"E","ě":"e","Ĝ":"G","Ǵ":"G","ĝ":"g","ǵ":"g","Ğ":"G","ğ":"g","Ġ":"G","ġ":"g","Ģ":"G","ģ":"g","Ĥ":"H","ĥ":"h","Ħ":"H","ħ":"h","Ḫ":"H","ḫ":"h","Ĩ":"I","ĩ":"i","Ī":"I","ī":"i","Ĭ":"I","ĭ":"i","Į":"I","į":"i","İ":"I","ı":"i","Ĳ":"IJ","ĳ":"ij","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","Ḱ":"K","ḱ":"k","K̆":"K","k̆":"k","Ĺ":"L","ĺ":"l","Ļ":"L","ļ":"l","Ľ":"L","ľ":"l","Ŀ":"L","ŀ":"l","Ł":"l","ł":"l","Ḿ":"M","ḿ":"m","M̆":"M","m̆":"m","Ń":"N","ń":"n","Ņ":"N","ņ":"n","Ň":"N","ň":"n","ŉ":"n","N̆":"N","n̆":"n","Ō":"O","ō":"o","Ŏ":"O","ŏ":"o","Ő":"O","ő":"o","Œ":"OE","œ":"oe","P̆":"P","p̆":"p","Ŕ":"R","ŕ":"r","Ŗ":"R","ŗ":"r","Ř":"R","ř":"r","R̆":"R","r̆":"r","Ȓ":"R","ȓ":"r","Ś":"S","ś":"s","Ŝ":"S","ŝ":"s","Ş":"S","Ș":"S","ș":"s","ş":"s","Š":"S","š":"s","Ţ":"T","ţ":"t","ț":"t","Ț":"T","Ť":"T","ť":"t","Ŧ":"T","ŧ":"t","T̆":"T","t̆":"t","Ũ":"U","ũ":"u","Ū":"U","ū":"u","Ŭ":"U","ŭ":"u","Ů":"U","ů":"u","Ű":"U","ű":"u","Ų":"U","ų":"u","Ȗ":"U","ȗ":"u","V̆":"V","v̆":"v","Ŵ":"W","ŵ":"w","Ẃ":"W","ẃ":"w","X̆":"X","x̆":"x","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Y̆":"Y","y̆":"y","Ź":"Z","ź":"z","Ż":"Z","ż":"z","Ž":"Z","ž":"z","ſ":"s","ƒ":"f","Ơ":"O","ơ":"o","Ư":"U","ư":"u","Ǎ":"A","ǎ":"a","Ǐ":"I","ǐ":"i","Ǒ":"O","ǒ":"o","Ǔ":"U","ǔ":"u","Ǖ":"U","ǖ":"u","Ǘ":"U","ǘ":"u","Ǚ":"U","ǚ":"u","Ǜ":"U","ǜ":"u","Ứ":"U","ứ":"u","Ṹ":"U","ṹ":"u","Ǻ":"A","ǻ":"a","Ǽ":"AE","ǽ":"ae","Ǿ":"O","ǿ":"o","Þ":"TH","þ":"th","Ṕ":"P","ṕ":"p","Ṥ":"S","ṥ":"s","X́":"X","x́":"x","Ѓ":"Г","ѓ":"г","Ќ":"К","ќ":"к","A̋":"A","a̋":"a","E̋":"E","e̋":"e","I̋":"I","i̋":"i","Ǹ":"N","ǹ":"n","Ồ":"O","ồ":"o","Ṑ":"O","ṑ":"o","Ừ":"U","ừ":"u","Ẁ":"W","ẁ":"w","Ỳ":"Y","ỳ":"y","Ȁ":"A","ȁ":"a","Ȅ":"E","ȅ":"e","Ȉ":"I","ȉ":"i","Ȍ":"O","ȍ":"o","Ȑ":"R","ȑ":"r","Ȕ":"U","ȕ":"u","B̌":"B","b̌":"b","Č̣":"C","č̣":"c","Ê̌":"E","ê̌":"e","F̌":"F","f̌":"f","Ǧ":"G","ǧ":"g","Ȟ":"H","ȟ":"h","J̌":"J","ǰ":"j","Ǩ":"K","ǩ":"k","M̌":"M","m̌":"m","P̌":"P","p̌":"p","Q̌":"Q","q̌":"q","Ř̩":"R","ř̩":"r","Ṧ":"S","ṧ":"s","V̌":"V","v̌":"v","W̌":"W","w̌":"w","X̌":"X","x̌":"x","Y̌":"Y","y̌":"y","A̧":"A","a̧":"a","B̧":"B","b̧":"b","Ḑ":"D","ḑ":"d","Ȩ":"E","ȩ":"e","Ɛ̧":"E","ɛ̧":"e","Ḩ":"H","ḩ":"h","I̧":"I","i̧":"i","Ɨ̧":"I","ɨ̧":"i","M̧":"M","m̧":"m","O̧":"O","o̧":"o","Q̧":"Q","q̧":"q","U̧":"U","u̧":"u","X̧":"X","x̧":"x","Z̧":"Z","z̧":"z","й":"и","Й":"И","ё":"е","Ё":"Е"},r=Object.keys(t).join("|"),o=new RegExp(r,"g"),u=new RegExp(r,"");function i(e){return t[e]}var a=function(e){return e.replace(o,i)};n.exports=a,n.exports.has=function(e){return!!e.match(u)},n.exports.remove=a;
/**
	 * @name match-sorter
	 * @license MIT license.
	 * @copyright (c) 2020 Kent C. Dodds
	 * <AUTHOR> C. Dodds <<EMAIL>> (https://kentcdodds.com)
	 */
const l={CASE_SENSITIVE_EQUAL:7,EQUAL:6,STARTS_WITH:5,WORD_STARTS_WITH:4,CONTAINS:3,ACRONYM:2,MATCHES:1,NO_MATCH:0},s=(e,n)=>String(e.rankedValue).localeCompare(String(n.rankedValue));function c(e,n,t){void 0===t&&(t={});const{keys:r,threshold:o=l.MATCHES,baseSort:u=s,sorter:i=(e=>e.sort(((e,n)=>f(e,n,u))))}=t,a=e.reduce((function(e,u,i){const a=function(e,n,t,r){if(!n){return{rankedValue:e,rank:A(e,t,r),keyIndex:-1,keyThreshold:r.threshold}}const o=function(e,n){const t=[];for(let r=0,o=n.length;r<o;r++){const o=n[r],u=d(o),i=h(e,o);for(let e=0,n=i.length;e<n;e++)t.push({itemValue:i[e],attributes:u})}return t}(e,n);return o.reduce(((e,n,o)=>{let{rank:u,rankedValue:i,keyIndex:a,keyThreshold:s}=e,{itemValue:c,attributes:f}=n,O=A(c,t,r),h=i;const{minRanking:E,maxRanking:d,threshold:T}=f;return O<E&&O>=l.MATCHES?O=E:O>d&&(O=d),O>u&&(u=O,a=o,s=T,h=c),{rankedValue:h,rank:u,keyIndex:a,keyThreshold:s}}),{rankedValue:e,rank:l.NO_MATCH,keyIndex:-1,keyThreshold:r.threshold})}(u,r,n,t),{rank:s,keyThreshold:c=o}=a;return s>=c&&e.push({...a,item:u,index:i}),e}),[]);return i(a).map((e=>{let{item:n}=e;return n}))}function A(e,n,t){return e=O(e,t),(n=O(n,t)).length>e.length?l.NO_MATCH:e===n?l.CASE_SENSITIVE_EQUAL:(e=e.toLowerCase())===(n=n.toLowerCase())?l.EQUAL:e.startsWith(n)?l.STARTS_WITH:e.includes(` ${n}`)?l.WORD_STARTS_WITH:e.includes(n)?l.CONTAINS:1===n.length?l.NO_MATCH:function(e){let n="";return e.split(" ").forEach((e=>{e.split("-").forEach((e=>{n+=e.substr(0,1)}))})),n}(e).includes(n)?l.ACRONYM:function(e,n){let t=0,r=0;function o(e,n,r){for(let o=r,u=n.length;o<u;o++){if(n[o]===e)return t+=1,o+1}return-1}function u(e){const r=1/e,o=t/n.length;return l.MATCHES+o*r}const i=o(n[0],e,0);if(i<0)return l.NO_MATCH;r=i;for(let t=1,u=n.length;t<u;t++){r=o(n[t],e,r);if(!(r>-1))return l.NO_MATCH}return u(r-i)}(e,n)}function f(e,n,t){const{rank:r,keyIndex:o}=e,{rank:u,keyIndex:i}=n;return r===u?o===i?t(e,n):o<i?-1:1:r>u?-1:1}function O(e,t){let{keepDiacritics:r}=t;return e=`${e}`,r||(e=n.exports(e)),e}function h(e,n){let t;if("object"==typeof n&&(n=n.key),"function"==typeof n)t=n(e);else if(null==e)t=null;else if(Object.hasOwnProperty.call(e,n))t=e[n];else{if(n.includes("."))return function(e,n){const t=e.split(".");let r=[n];for(let e=0,n=t.length;e<n;e++){const n=t[e];let o=[];for(let e=0,t=r.length;e<t;e++){const t=r[e];if(null!=t)if(Object.hasOwnProperty.call(t,n)){const e=t[n];null!=e&&o.push(e)}else"*"===n&&(o=o.concat(t))}r=o}if(Array.isArray(r[0])){return[].concat(...r)}return r}(n,e);t=null}return null==t?[]:Array.isArray(t)?t:[String(t)]}c.rankings=l;const E={maxRanking:1/0,minRanking:-1/0};function d(e){return"string"==typeof e?E:{...E,...e}}e.defaultBaseSortFn=s,e.matchSorter=c,e.rankings=l,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=match-sorter.umd.min.js.map
