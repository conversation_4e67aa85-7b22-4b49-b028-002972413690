const fs = require('fs');
const path = require('path');

class ReportAnalyzer {
    constructor() {
        this.reportPath = 'website-exploration-report.json';
        this.readmePath = 'README.md';
    }

    loadReport() {
        try {
            if (!fs.existsSync(this.reportPath)) {
                console.log('❌ No exploration report found. Run "npm run explore" first.');
                return null;
            }
            
            const reportData = fs.readFileSync(this.reportPath, 'utf8');
            return JSON.parse(reportData);
        } catch (error) {
            console.error('❌ Error loading report:', error.message);
            return null;
        }
    }

    analyzeFeatures(report) {
        const features = {
            authentication: false,
            dashboard: false,
            charts: false,
            maps: false,
            tables: false,
            forms: false,
            navigation: false,
            realTimeData: false,
            reports: false,
            alerts: false,
            userManagement: false,
            dataExport: false
        };

        const keywords = {
            authentication: ['login', 'signin', 'auth', 'password', 'user'],
            dashboard: ['dashboard', 'overview', 'summary', 'home'],
            charts: ['chart', 'graph', 'plot', 'visualization'],
            maps: ['map', 'gis', 'location', 'geographic'],
            tables: ['table', 'grid', 'data', 'list'],
            forms: ['form', 'input', 'submit', 'create', 'edit'],
            navigation: ['menu', 'nav', 'sidebar', 'header'],
            realTimeData: ['real-time', 'live', 'current', 'now'],
            reports: ['report', 'export', 'download', 'pdf'],
            alerts: ['alert', 'notification', 'warning', 'alarm'],
            userManagement: ['user', 'admin', 'role', 'permission'],
            dataExport: ['export', 'download', 'csv', 'excel']
        };

        // Analyze navigation items
        report.findings.navigation.forEach(navItem => {
            const text = navItem.text.toLowerCase();
            Object.keys(keywords).forEach(feature => {
                if (keywords[feature].some(keyword => text.includes(keyword))) {
                    features[feature] = true;
                }
            });
        });

        // Analyze page content
        report.findings.pages.forEach(page => {
            const title = page.title.toLowerCase();
            const headings = page.headings.map(h => h.text.toLowerCase()).join(' ');
            const content = title + ' ' + headings;

            Object.keys(keywords).forEach(feature => {
                if (keywords[feature].some(keyword => content.includes(keyword))) {
                    features[feature] = true;
                }
            });

            // Check for specific elements
            if (page.charts.length > 0) features.charts = true;
            if (page.maps.length > 0) features.maps = true;
            if (page.tables.length > 0) features.tables = true;
            if (page.forms.length > 0) features.forms = true;
        });

        return features;
    }

    generateTechnologyStack(report) {
        const technologies = {
            frontend: [],
            libraries: [],
            frameworks: []
        };

        // Analyze from API calls and page structure
        const apiUrls = report.findings.apis.map(api => api.url);
        
        // Check for common patterns
        if (apiUrls.some(url => url.includes('api'))) {
            technologies.frontend.push('REST API Integration');
        }

        // Check for chart libraries
        report.findings.pages.forEach(page => {
            page.charts.forEach(chart => {
                if (chart.className) {
                    if (chart.className.includes('chart')) technologies.libraries.push('Chart.js/D3.js');
                    if (chart.className.includes('highcharts')) technologies.libraries.push('Highcharts');
                }
            });

            page.maps.forEach(map => {
                if (map.selector.includes('leaflet')) technologies.libraries.push('Leaflet Maps');
                if (map.selector.includes('ol-')) technologies.libraries.push('OpenLayers');
            });
        });

        return technologies;
    }

    generateDetailedFeatureList(report) {
        const features = [];

        // Navigation-based features
        const navGroups = {};
        report.findings.navigation.forEach(navItem => {
            const category = this.categorizeNavItem(navItem.text);
            if (!navGroups[category]) navGroups[category] = [];
            navGroups[category].push(navItem.text);
        });

        Object.keys(navGroups).forEach(category => {
            features.push({
                category: category,
                items: navGroups[category]
            });
        });

        // Page-based features
        report.findings.pages.forEach(page => {
            if (page.forms.length > 0) {
                features.push({
                    category: 'Data Input',
                    items: page.forms.map(form => `Form with ${form.inputs.length} inputs`)
                });
            }

            if (page.tables.length > 0) {
                features.push({
                    category: 'Data Display',
                    items: page.tables.map(table => `Table with ${table.rowCount} rows`)
                });
            }

            if (page.charts.length > 0) {
                features.push({
                    category: 'Data Visualization',
                    items: [`${page.charts.length} chart(s)/graph(s)`]
                });
            }

            if (page.maps.length > 0) {
                features.push({
                    category: 'Geographic Features',
                    items: [`${page.maps.length} map(s)`]
                });
            }
        });

        return features;
    }

    categorizeNavItem(text) {
        const categories = {
            'Dashboard': ['dashboard', 'home', 'overview', 'summary'],
            'Data Management': ['data', 'stations', 'sensors', 'devices'],
            'Monitoring': ['monitor', 'real-time', 'live', 'current'],
            'Reports': ['report', 'analytics', 'statistics', 'export'],
            'Maps': ['map', 'location', 'geographic', 'gis'],
            'Alerts': ['alert', 'notification', 'alarm', 'warning'],
            'Administration': ['admin', 'settings', 'config', 'user', 'manage'],
            'Help': ['help', 'support', 'documentation', 'about']
        };

        const lowerText = text.toLowerCase();
        for (const [category, keywords] of Object.entries(categories)) {
            if (keywords.some(keyword => lowerText.includes(keyword))) {
                return category;
            }
        }
        return 'Other';
    }

    generateUpdatedReadme(report) {
        const features = this.analyzeFeatures(report);
        const technologies = this.generateTechnologyStack(report);
        const detailedFeatures = this.generateDetailedFeatureList(report);

        let readme = `# Hydroview

A comprehensive radar-based water monitoring and management system.

## Overview

Hydroview is a web-based application that provides real-time monitoring and analysis of water-related data using radar technology. The system offers advanced visualization and management capabilities for hydrological monitoring.

*This documentation was generated from automated exploration of the live system on ${new Date().toLocaleDateString()}.*

## 🌟 Key Features Discovered

`;

        // Add discovered features
        detailedFeatures.forEach(featureGroup => {
            readme += `### ${featureGroup.category}\n`;
            featureGroup.items.forEach(item => {
                readme += `- ${item}\n`;
            });
            readme += '\n';
        });

        readme += `## 🔧 Technology Stack

Based on the exploration, the system uses:

### Frontend Technologies
`;
        if (technologies.frontend.length > 0) {
            technologies.frontend.forEach(tech => {
                readme += `- ${tech}\n`;
            });
        } else {
            readme += `- Modern web technologies (HTML5, CSS3, JavaScript)
- Responsive design framework
- Single Page Application (SPA) architecture
`;
        }

        readme += `
### Libraries & Frameworks
`;
        if (technologies.libraries.length > 0) {
            technologies.libraries.forEach(lib => {
                readme += `- ${lib}\n`;
            });
        } else {
            readme += `- Chart/Graph visualization library
- Interactive mapping library
- UI component framework
`;
        }

        readme += `
## 📊 System Capabilities

### Data Visualization
- **Charts**: ${report.summary.totalCharts} chart/graph components found
- **Tables**: ${report.summary.totalTables} data tables for structured information
- **Maps**: ${report.summary.totalMaps} interactive map components

### User Interface
- **Pages**: ${report.summary.totalPages} main application pages
- **Navigation**: ${report.summary.totalNavItems} navigation menu items
- **Forms**: ${report.summary.totalForms} data input forms

## 🗺️ Application Structure

### Main Navigation Areas
`;

        // Add navigation structure
        const navByCategory = {};
        report.findings.navigation.forEach(navItem => {
            const category = this.categorizeNavItem(navItem.text);
            if (!navByCategory[category]) navByCategory[category] = [];
            navByCategory[category].push(navItem.text);
        });

        Object.keys(navByCategory).forEach(category => {
            readme += `
#### ${category}
`;
            navByCategory[category].forEach(item => {
                readme += `- ${item}\n`;
            });
        });

        readme += `
## 🔗 API Integration

The system integrates with ${report.findings.apis.length} API endpoints for data retrieval and management.

## 📱 Screenshots

Screenshots of the application have been captured during exploration and saved in the \`screenshots/\` directory.

## 🚀 Getting Started

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Network access to the Geolux radar systems
- Valid user credentials

### Access
1. Navigate to: https://hv2.geolux-radars.com/#/
2. Enter your credentials
3. Explore the various monitoring and management features

## 📞 Support

For technical support and access requests, contact the system administrators.

---

*Last updated: ${new Date().toISOString()}*
*Generated from automated website exploration*
`;

        return readme;
    }

    async updateReadme(report) {
        try {
            const newReadme = this.generateUpdatedReadme(report);
            fs.writeFileSync(this.readmePath, newReadme);
            console.log('✅ README.md updated with discovered features');
        } catch (error) {
            console.error('❌ Error updating README:', error.message);
        }
    }

    async analyze() {
        console.log('🔍 Analyzing exploration report...');
        
        const report = this.loadReport();
        if (!report) return;

        console.log('📊 Report Summary:');
        console.log(`- Total Pages Explored: ${report.summary.totalPages}`);
        console.log(`- Navigation Items: ${report.summary.totalNavItems}`);
        console.log(`- Forms Found: ${report.summary.totalForms}`);
        console.log(`- Tables Found: ${report.summary.totalTables}`);
        console.log(`- Charts Found: ${report.summary.totalCharts}`);
        console.log(`- Maps Found: ${report.summary.totalMaps}`);
        console.log(`- API Calls Detected: ${report.findings.apis.length}`);

        const features = this.analyzeFeatures(report);
        console.log('\n🌟 Detected Features:');
        Object.keys(features).forEach(feature => {
            if (features[feature]) {
                console.log(`✅ ${feature}`);
            }
        });

        await this.updateReadme(report);
        
        console.log('\n🎉 Analysis complete! Check the updated README.md file.');
    }
}

// Main execution
async function main() {
    const analyzer = new ReportAnalyzer();
    await analyzer.analyze();
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = ReportAnalyzer;
