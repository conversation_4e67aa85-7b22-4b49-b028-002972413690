const { chromium } = require('playwright');
const fs = require('fs');

class EnhancedWebsiteExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            pages: [],
            navigation: [],
            apis: [],
            features: [],
            userInterface: {},
            dataStructures: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
    }

    async init() {
        console.log('🚀 Initializing enhanced browser exploration...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 2000
        });
        this.page = await this.browser.newPage();
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Enhanced network monitoring
        this.page.on('request', request => {
            if (request.url().includes('api') || request.url().includes('data')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    timestamp: new Date().toISOString()
                });
            }
        });

        this.page.on('response', async response => {
            if (response.url().includes('api') && response.status() === 200) {
                try {
                    const contentType = response.headers()['content-type'];
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        this.findings.dataStructures.push({
                            url: response.url(),
                            data: data,
                            timestamp: new Date().toISOString()
                        });
                    }
                } catch (e) {
                    // Ignore parsing errors
                }
            }
        });
    }

    async login() {
        console.log('🔐 Logging in...');
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
        await this.page.waitForTimeout(5000);

        // Login process
        await this.page.fill('input[type="text"]', this.credentials.username);
        await this.page.fill('input[type="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        
        await this.page.waitForTimeout(8000); // Wait for dashboard to load
        await this.page.screenshot({ path: 'screenshots/dashboard.png', fullPage: true });
        
        console.log('✅ Login successful, dashboard loaded');
        return true;
    }

    async exploreQuasarInterface() {
        console.log('🎨 Exploring Quasar interface elements...');
        
        // Wait for Quasar components to load
        await this.page.waitForTimeout(5000);

        // Look for Quasar-specific elements
        const quasarElements = await this.page.evaluate(() => {
            const elements = {
                buttons: [],
                menus: [],
                drawers: [],
                toolbars: [],
                cards: [],
                dialogs: []
            };

            // Find Q-buttons
            document.querySelectorAll('.q-btn').forEach(btn => {
                elements.buttons.push({
                    text: btn.textContent.trim(),
                    classes: btn.className,
                    visible: btn.offsetParent !== null
                });
            });

            // Find Q-drawers (side navigation)
            document.querySelectorAll('.q-drawer').forEach(drawer => {
                elements.drawers.push({
                    classes: drawer.className,
                    visible: drawer.offsetParent !== null
                });
            });

            // Find Q-toolbars
            document.querySelectorAll('.q-toolbar').forEach(toolbar => {
                elements.toolbars.push({
                    text: toolbar.textContent.trim(),
                    classes: toolbar.className
                });
            });

            // Find Q-cards
            document.querySelectorAll('.q-card').forEach(card => {
                elements.cards.push({
                    text: card.textContent.trim().substring(0, 100),
                    classes: card.className
                });
            });

            return elements;
        });

        this.findings.userInterface = quasarElements;
        console.log(`Found ${quasarElements.buttons.length} buttons, ${quasarElements.cards.length} cards`);
    }

    async clickMenuButton() {
        console.log('📱 Attempting to open navigation menu...');
        
        try {
            // Look for menu buttons
            const menuSelectors = [
                'button:has-text("menu")',
                '.q-btn:has-text("menu")',
                '[aria-label="menu"]',
                '.menu-btn',
                '.hamburger'
            ];

            for (const selector of menuSelectors) {
                try {
                    const menuBtn = await this.page.$(selector);
                    if (menuBtn) {
                        console.log(`Clicking menu button: ${selector}`);
                        await menuBtn.click();
                        await this.page.waitForTimeout(3000);
                        await this.page.screenshot({ path: 'screenshots/menu-opened.png', fullPage: true });
                        return true;
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (error) {
            console.log('Could not open menu:', error.message);
        }
        return false;
    }

    async exploreNavigation() {
        console.log('🧭 Exploring navigation structure...');
        
        // Try to open menu first
        await this.clickMenuButton();
        
        // Look for navigation items
        const navItems = await this.page.evaluate(() => {
            const items = [];
            
            // Look for various navigation patterns
            const selectors = [
                '.q-item',
                '.q-list .q-item',
                '.nav-item',
                'a[href]',
                'button[role="menuitem"]',
                '.menu-item'
            ];

            selectors.forEach(selector => {
                document.querySelectorAll(selector).forEach(item => {
                    const text = item.textContent.trim();
                    const href = item.getAttribute('href');
                    const onclick = item.getAttribute('onclick');
                    
                    if (text && text.length > 0 && text.length < 100) {
                        items.push({
                            text: text,
                            href: href,
                            onclick: onclick,
                            selector: selector,
                            visible: item.offsetParent !== null
                        });
                    }
                });
            });

            return items;
        });

        this.findings.navigation = navItems;
        console.log(`Found ${navItems.length} navigation items`);
        
        // Try clicking on visible navigation items
        for (let i = 0; i < Math.min(navItems.length, 8); i++) {
            const item = navItems[i];
            if (item.visible && item.text.length > 2) {
                try {
                    console.log(`Navigating to: ${item.text}`);
                    await this.page.click(`text="${item.text}"`);
                    await this.page.waitForTimeout(4000);
                    
                    const url = this.page.url();
                    const title = await this.page.title();
                    
                    await this.page.screenshot({ 
                        path: `screenshots/nav-${i}-${item.text.replace(/[^a-zA-Z0-9]/g, '-')}.png`, 
                        fullPage: true 
                    });
                    
                    // Explore this page
                    await this.exploreCurrentPage(item.text);
                    
                } catch (error) {
                    console.log(`Error navigating to ${item.text}:`, error.message);
                }
            }
        }
    }

    async exploreCurrentPage(pageName = 'Unknown') {
        console.log(`📄 Exploring page: ${pageName}`);
        
        await this.page.waitForTimeout(3000);
        
        const pageData = await this.page.evaluate(() => {
            const data = {
                url: window.location.href,
                title: document.title,
                headings: [],
                tables: [],
                forms: [],
                charts: [],
                maps: [],
                widgets: []
            };

            // Get headings
            document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(h => {
                data.headings.push({
                    level: h.tagName,
                    text: h.textContent.trim()
                });
            });

            // Look for data tables
            document.querySelectorAll('table, .q-table').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
                const rows = table.querySelectorAll('tr').length;
                data.tables.push({
                    id: table.id || `table-${index}`,
                    headers: headers,
                    rowCount: rows
                });
            });

            // Look for forms
            document.querySelectorAll('form, .q-form').forEach((form, index) => {
                const inputs = Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
                    type: input.type,
                    name: input.name,
                    placeholder: input.placeholder
                }));
                data.forms.push({
                    id: form.id || `form-${index}`,
                    inputs: inputs
                });
            });

            // Look for charts/graphs
            document.querySelectorAll('canvas, svg, .chart, .graph').forEach((chart, index) => {
                data.charts.push({
                    type: chart.tagName,
                    id: chart.id || `chart-${index}`,
                    classes: chart.className
                });
            });

            // Look for maps
            document.querySelectorAll('.map, #map, .leaflet-container, .ol-viewport').forEach((map, index) => {
                data.maps.push({
                    id: map.id || `map-${index}`,
                    classes: map.className
                });
            });

            // Look for Quasar widgets/cards
            document.querySelectorAll('.q-card, .widget, .dashboard-widget').forEach((widget, index) => {
                data.widgets.push({
                    id: widget.id || `widget-${index}`,
                    text: widget.textContent.trim().substring(0, 200),
                    classes: widget.className
                });
            });

            return data;
        });

        pageData.pageName = pageName;
        this.findings.pages.push(pageData);
        
        console.log(`✅ Page explored: ${pageData.tables.length} tables, ${pageData.charts.length} charts, ${pageData.widgets.length} widgets`);
    }

    async analyzeAPIs() {
        console.log('🔍 Analyzing API structure...');
        
        const apiAnalysis = {
            endpoints: {},
            dataTypes: [],
            features: []
        };

        this.findings.apis.forEach(api => {
            const url = new URL(api.url);
            const path = url.pathname;
            
            if (!apiAnalysis.endpoints[path]) {
                apiAnalysis.endpoints[path] = [];
            }
            apiAnalysis.endpoints[path].push(api.method);
        });

        // Analyze data structures
        this.findings.dataStructures.forEach(ds => {
            if (ds.data && typeof ds.data === 'object') {
                const keys = Object.keys(ds.data);
                apiAnalysis.dataTypes.push({
                    endpoint: ds.url,
                    structure: keys
                });
            }
        });

        this.findings.apiAnalysis = apiAnalysis;
        console.log(`Analyzed ${Object.keys(apiAnalysis.endpoints).length} API endpoints`);
    }

    async generateReport() {
        console.log('📊 Generating comprehensive report...');
        
        await this.analyzeAPIs();
        
        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            technology: 'Quasar Framework (Vue.js)',
            summary: {
                totalPages: this.findings.pages.length,
                totalNavItems: this.findings.navigation.length,
                totalAPIs: this.findings.apis.length,
                totalButtons: this.findings.userInterface.buttons?.length || 0,
                totalCards: this.findings.userInterface.cards?.length || 0
            },
            findings: this.findings
        };

        fs.writeFileSync('enhanced-exploration-report.json', JSON.stringify(report, null, 2));
        console.log('✅ Enhanced report saved');
        return report;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new EnhancedWebsiteExplorer();
    
    try {
        await explorer.init();
        await explorer.login();
        await explorer.exploreQuasarInterface();
        await explorer.exploreNavigation();
        await explorer.exploreCurrentPage('Dashboard');
        const report = await explorer.generateReport();
        
        console.log('🎉 Enhanced exploration completed!');
        console.log('📊 Summary:', report.summary);
    } catch (error) {
        console.error('❌ Exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = EnhancedWebsiteExplorer;
