const { chromium } = require('playwright');
const fs = require('fs');

class DeepNavigationExplorer {
    constructor() {
        this.browser = null;
        this.page = null;
        this.findings = {
            pages: [],
            navigation: [],
            apis: [],
            features: [],
            detailedSections: {},
            userInterface: {},
            dataStructures: [],
            screenshots: []
        };
        this.credentials = {
            username: 'deepak.gupta',
            password: 'dtpl1234'
        };
        this.baseUrl = 'https://hv2.geolux-radars.com/#/';
        this.screenshotCounter = 1;
    }

    async init() {
        console.log('🚀 Initializing deep navigation exploration...');
        this.browser = await chromium.launch({ 
            headless: false,
            slowMo: 1500
        });
        this.page = await this.browser.newPage();
        await this.page.setViewportSize({ width: 1920, height: 1080 });
        
        // Enhanced network monitoring
        this.page.on('request', request => {
            if (request.url().includes('api') || request.url().includes('data')) {
                this.findings.apis.push({
                    url: request.url(),
                    method: request.method(),
                    timestamp: new Date().toISOString(),
                    headers: request.headers()
                });
            }
        });

        this.page.on('response', async response => {
            if (response.url().includes('api') && response.status() === 200) {
                try {
                    const contentType = response.headers()['content-type'];
                    if (contentType && contentType.includes('application/json')) {
                        const data = await response.json();
                        this.findings.dataStructures.push({
                            url: response.url(),
                            data: data,
                            timestamp: new Date().toISOString()
                        });
                    }
                } catch (e) {
                    // Ignore parsing errors
                }
            }
        });
    }

    async takeScreenshot(name, description) {
        const filename = `screenshots/${String(this.screenshotCounter).padStart(2, '0')}-${name}.png`;
        await this.page.screenshot({ path: filename, fullPage: true });
        this.findings.screenshots.push({
            filename: filename,
            description: description,
            timestamp: new Date().toISOString()
        });
        this.screenshotCounter++;
        console.log(`📸 Screenshot saved: ${filename}`);
    }

    async login() {
        console.log('🔐 Logging in...');
        await this.page.goto(this.baseUrl, { waitUntil: 'networkidle' });
        await this.page.waitForTimeout(3000);

        await this.takeScreenshot('login-page', 'Initial login interface');

        // Login process
        await this.page.fill('input[type="text"]', this.credentials.username);
        await this.page.fill('input[type="password"]', this.credentials.password);
        await this.page.click('button[type="submit"]');
        
        await this.page.waitForTimeout(8000); // Wait for dashboard to load
        await this.takeScreenshot('dashboard-main', 'Main dashboard after login');
        
        console.log('✅ Login successful, dashboard loaded');
        return true;
    }

    async openSidebar() {
        console.log('📱 Opening sidebar navigation...');
        
        try {
            // Multiple strategies to open sidebar
            const sidebarSelectors = [
                'button:has-text("menu")',
                '.q-btn:has([class*="menu"])',
                '[aria-label="menu"]',
                '.menu-btn',
                '.hamburger-menu',
                'button[class*="menu"]'
            ];

            for (const selector of sidebarSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    for (const element of elements) {
                        const isVisible = await element.isVisible();
                        if (isVisible) {
                            console.log(`Clicking menu button: ${selector}`);
                            await element.click();
                            await this.page.waitForTimeout(2000);
                            await this.takeScreenshot('sidebar-opened', 'Sidebar navigation opened');
                            return true;
                        }
                    }
                } catch (e) {
                    continue;
                }
            }

            // Try clicking on any visible menu icon
            const menuIcons = await this.page.$$('i:has-text("menu"), span:has-text("menu")');
            for (const icon of menuIcons) {
                try {
                    await icon.click();
                    await this.page.waitForTimeout(2000);
                    await this.takeScreenshot('sidebar-opened-alt', 'Sidebar opened via icon');
                    return true;
                } catch (e) {
                    continue;
                }
            }

        } catch (error) {
            console.log('Could not open sidebar:', error.message);
        }
        return false;
    }

    async discoverNavigationItems() {
        console.log('🔍 Discovering all navigation items...');
        
        await this.openSidebar();
        await this.page.waitForTimeout(3000);

        // Get all possible navigation items
        const navItems = await this.page.evaluate(() => {
            const items = [];
            
            // Look for various navigation patterns
            const selectors = [
                '.q-item',
                '.q-list .q-item',
                '.nav-item',
                'a[href*="#"]',
                'button[role="menuitem"]',
                '.menu-item',
                '[class*="nav"]',
                '.sidebar a',
                '.drawer a',
                '.q-drawer a',
                '.q-drawer .q-item'
            ];

            selectors.forEach(selector => {
                try {
                    document.querySelectorAll(selector).forEach((item, index) => {
                        const text = item.textContent?.trim();
                        const href = item.getAttribute('href');
                        const onclick = item.getAttribute('onclick');
                        const classes = item.className;
                        
                        if (text && text.length > 0 && text.length < 100) {
                            items.push({
                                text: text,
                                href: href,
                                onclick: onclick,
                                selector: selector,
                                classes: classes,
                                visible: item.offsetParent !== null,
                                index: index
                            });
                        }
                    });
                } catch (e) {
                    console.log('Error with selector:', selector, e.message);
                }
            });

            return items;
        });

        // Remove duplicates based on text
        const uniqueNavItems = navItems.filter((item, index, self) => 
            index === self.findIndex(t => t.text === item.text)
        );

        this.findings.navigation = uniqueNavItems;
        console.log(`Found ${uniqueNavItems.length} unique navigation items:`);
        uniqueNavItems.forEach(item => {
            console.log(`  - ${item.text} (${item.selector})`);
        });

        return uniqueNavItems;
    }

    async navigateToSection(navItem, sectionIndex) {
        console.log(`\n🧭 Navigating to: ${navItem.text}`);
        
        try {
            // Ensure sidebar is open
            await this.openSidebar();
            await this.page.waitForTimeout(2000);

            // Multiple strategies to click the navigation item
            const clickStrategies = [
                // Strategy 1: Click by exact text
                async () => {
                    await this.page.click(`text="${navItem.text}"`);
                },
                // Strategy 2: Click by partial text
                async () => {
                    await this.page.click(`text*="${navItem.text.substring(0, 10)}"`);
                },
                // Strategy 3: Click by selector and index
                async () => {
                    const elements = await this.page.$$(navItem.selector);
                    if (elements[navItem.index]) {
                        await elements[navItem.index].click();
                    }
                },
                // Strategy 4: Click by href if available
                async () => {
                    if (navItem.href) {
                        await this.page.click(`[href="${navItem.href}"]`);
                    }
                },
                // Strategy 5: Navigate directly to URL if it's a hash route
                async () => {
                    if (navItem.href && navItem.href.startsWith('#')) {
                        await this.page.goto(this.baseUrl + navItem.href.substring(1));
                    }
                }
            ];

            let success = false;
            for (let i = 0; i < clickStrategies.length && !success; i++) {
                try {
                    await clickStrategies[i]();
                    success = true;
                    console.log(`✅ Navigation successful using strategy ${i + 1}`);
                } catch (e) {
                    console.log(`Strategy ${i + 1} failed:`, e.message);
                }
            }

            if (!success) {
                console.log(`❌ Could not navigate to ${navItem.text}`);
                return false;
            }

            // Wait for page to load
            await this.page.waitForTimeout(5000);
            
            // Take screenshot
            const safeName = navItem.text.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
            await this.takeScreenshot(`section-${sectionIndex}-${safeName}`, `${navItem.text} section`);

            // Explore this section in detail
            await this.exploreCurrentSection(navItem.text);

            return true;

        } catch (error) {
            console.error(`❌ Error navigating to ${navItem.text}:`, error.message);
            return false;
        }
    }

    async exploreCurrentSection(sectionName) {
        console.log(`📄 Exploring section: ${sectionName}`);
        
        await this.page.waitForTimeout(3000);
        
        const sectionData = await this.page.evaluate(() => {
            const data = {
                url: window.location.href,
                title: document.title,
                headings: [],
                tables: [],
                forms: [],
                charts: [],
                maps: [],
                widgets: [],
                buttons: [],
                inputs: [],
                lists: [],
                modals: [],
                tabs: []
            };

            // Get headings
            document.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(h => {
                data.headings.push({
                    level: h.tagName,
                    text: h.textContent.trim()
                });
            });

            // Get tables
            document.querySelectorAll('table, .q-table, .data-table').forEach((table, index) => {
                const headers = Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim());
                const rows = table.querySelectorAll('tr').length;
                data.tables.push({
                    id: table.id || `table-${index}`,
                    headers: headers,
                    rowCount: rows,
                    classes: table.className
                });
            });

            // Get forms
            document.querySelectorAll('form, .q-form').forEach((form, index) => {
                const inputs = Array.from(form.querySelectorAll('input, select, textarea')).map(input => ({
                    type: input.type,
                    name: input.name,
                    placeholder: input.placeholder,
                    required: input.required
                }));
                data.forms.push({
                    id: form.id || `form-${index}`,
                    inputs: inputs,
                    action: form.action,
                    method: form.method
                });
            });

            // Get charts/graphs
            document.querySelectorAll('canvas, svg, .chart, .graph, .plot').forEach((chart, index) => {
                data.charts.push({
                    type: chart.tagName,
                    id: chart.id || `chart-${index}`,
                    classes: chart.className,
                    width: chart.width || chart.clientWidth,
                    height: chart.height || chart.clientHeight
                });
            });

            // Get maps
            document.querySelectorAll('.map, #map, .leaflet-container, .ol-viewport, .mapbox').forEach((map, index) => {
                data.maps.push({
                    id: map.id || `map-${index}`,
                    classes: map.className,
                    width: map.clientWidth,
                    height: map.clientHeight
                });
            });

            // Get buttons
            document.querySelectorAll('button, .q-btn, input[type="button"], input[type="submit"]').forEach((btn, index) => {
                data.buttons.push({
                    text: btn.textContent?.trim() || btn.value,
                    type: btn.type,
                    classes: btn.className,
                    id: btn.id || `button-${index}`,
                    disabled: btn.disabled
                });
            });

            // Get input fields
            document.querySelectorAll('input, textarea, select').forEach((input, index) => {
                data.inputs.push({
                    type: input.type,
                    name: input.name,
                    placeholder: input.placeholder,
                    value: input.value,
                    required: input.required,
                    id: input.id || `input-${index}`
                });
            });

            // Get lists
            document.querySelectorAll('ul, ol, .q-list').forEach((list, index) => {
                const items = Array.from(list.querySelectorAll('li, .q-item')).map(li => li.textContent.trim());
                data.lists.push({
                    id: list.id || `list-${index}`,
                    type: list.tagName,
                    items: items.slice(0, 10), // Limit to first 10 items
                    totalItems: items.length
                });
            });

            // Get tabs
            document.querySelectorAll('.q-tabs, .tabs, [role="tablist"]').forEach((tabContainer, index) => {
                const tabs = Array.from(tabContainer.querySelectorAll('.q-tab, .tab, [role="tab"]')).map(tab => tab.textContent.trim());
                data.tabs.push({
                    id: tabContainer.id || `tabs-${index}`,
                    tabs: tabs
                });
            });

            // Get modals/dialogs
            document.querySelectorAll('.q-dialog, .modal, .dialog').forEach((modal, index) => {
                data.modals.push({
                    id: modal.id || `modal-${index}`,
                    classes: modal.className,
                    visible: modal.offsetParent !== null
                });
            });

            return data;
        });

        sectionData.sectionName = sectionName;
        sectionData.explorationTime = new Date().toISOString();
        
        this.findings.detailedSections[sectionName] = sectionData;
        this.findings.pages.push(sectionData);
        
        console.log(`✅ Section explored: ${sectionData.tables.length} tables, ${sectionData.charts.length} charts, ${sectionData.buttons.length} buttons, ${sectionData.forms.length} forms`);
        
        // Look for sub-navigation or additional actions
        await this.exploreSubNavigation(sectionName);
    }

    async exploreSubNavigation(sectionName) {
        console.log(`🔍 Looking for sub-navigation in ${sectionName}...`);
        
        // Look for tabs, sub-menus, or action buttons
        const subNavItems = await this.page.evaluate(() => {
            const items = [];
            
            // Look for tabs
            document.querySelectorAll('.q-tab, .tab, [role="tab"]').forEach(tab => {
                if (tab.textContent.trim()) {
                    items.push({
                        type: 'tab',
                        text: tab.textContent.trim(),
                        clickable: true
                    });
                }
            });

            // Look for action buttons
            document.querySelectorAll('button, .q-btn').forEach(btn => {
                const text = btn.textContent.trim();
                if (text && text.length < 50 && !text.includes('menu')) {
                    items.push({
                        type: 'button',
                        text: text,
                        clickable: true
                    });
                }
            });

            return items;
        });

        if (subNavItems.length > 0) {
            console.log(`Found ${subNavItems.length} sub-navigation items in ${sectionName}`);
            
            // Try clicking on a few sub-navigation items
            for (let i = 0; i < Math.min(subNavItems.length, 3); i++) {
                const item = subNavItems[i];
                try {
                    console.log(`  Clicking: ${item.text}`);
                    await this.page.click(`text="${item.text}"`);
                    await this.page.waitForTimeout(3000);
                    
                    const safeName = `${sectionName}-${item.text}`.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase();
                    await this.takeScreenshot(`sub-${safeName}`, `${sectionName} - ${item.text}`);
                    
                } catch (e) {
                    console.log(`  Could not click ${item.text}:`, e.message);
                }
            }
        }
    }

    async generateComprehensiveReport() {
        console.log('📊 Generating comprehensive exploration report...');
        
        const report = {
            timestamp: new Date().toISOString(),
            website: this.baseUrl,
            technology: 'Quasar Framework (Vue.js)',
            exploration: {
                totalSections: Object.keys(this.findings.detailedSections).length,
                totalScreenshots: this.findings.screenshots.length,
                totalAPIs: this.findings.apis.length,
                totalNavItems: this.findings.navigation.length
            },
            findings: this.findings,
            summary: {
                sectionsExplored: Object.keys(this.findings.detailedSections),
                screenshotsCaptured: this.findings.screenshots.map(s => s.filename),
                apiEndpoints: [...new Set(this.findings.apis.map(api => new URL(api.url).pathname))],
                navigationStructure: this.findings.navigation.map(nav => nav.text)
            }
        };

        fs.writeFileSync('deep-navigation-report.json', JSON.stringify(report, null, 2));
        console.log('✅ Comprehensive report saved to deep-navigation-report.json');
        return report;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
        }
    }
}

// Main execution
async function main() {
    const explorer = new DeepNavigationExplorer();
    
    try {
        // Create screenshots directory
        if (!fs.existsSync('screenshots')) {
            fs.mkdirSync('screenshots');
        }

        await explorer.init();
        await explorer.login();
        
        // Discover all navigation items
        const navItems = await explorer.discoverNavigationItems();
        
        // Navigate through each section systematically
        for (let i = 0; i < navItems.length; i++) {
            const navItem = navItems[i];
            if (navItem.visible && navItem.text.length > 2) {
                await explorer.navigateToSection(navItem, i);
                await explorer.page.waitForTimeout(2000); // Brief pause between sections
            }
        }
        
        const report = await explorer.generateComprehensiveReport();
        
        console.log('\n🎉 Deep navigation exploration completed!');
        console.log('📊 Summary:');
        console.log(`  - Sections explored: ${report.exploration.totalSections}`);
        console.log(`  - Screenshots taken: ${report.exploration.totalScreenshots}`);
        console.log(`  - API calls captured: ${report.exploration.totalAPIs}`);
        console.log(`  - Navigation items found: ${report.exploration.totalNavItems}`);
        
    } catch (error) {
        console.error('❌ Deep exploration failed:', error);
    } finally {
        await explorer.close();
    }
}

if (require.main === module) {
    main();
}

module.exports = DeepNavigationExplorer;
